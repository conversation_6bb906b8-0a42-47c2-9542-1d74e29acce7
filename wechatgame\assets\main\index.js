window.__require=function t(e,o,n){function i(r,s){if(!o[r]){if(!e[r]){var c=r.split("/");if(c=c[c.length-1],!e[c]){var l="function"==typeof __require&&__require;if(!s&&l)return l(c,!0);if(a)return a(c,!0);throw new Error("Cannot find module '"+r+"'")}r=c}var u=o[r]={exports:{}};e[r][0].call(u.exports,function(t){return i(e[r][1][t]||t)},u,u.exports,t,e,o,n)}return o[r].exports}for(var a="function"==typeof __require&&__require,r=0;r<n.length;r++)i(n[r]);return i}({AnimalColorBtnTest:[function(t,e,o){"use strict";cc._RF.push(e,"4f3ec6SdyhLUp1noCX4E2kT","AnimalColorBtnTest");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=(r.property,function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.start=function(){var t=this;this.scheduleOnce(function(){t.runTests()},3)},e.prototype.runTests=function(){cc.log("========== AnimalColorBtn \u6d4b\u8bd5\u5f00\u59cb ==========");var t=cc.find("Canvas/GameUI");if(t){var e=cc.find("AnimalColorBtn",t);if(e){var o=e.getComponent("AnimalColorBtn");o?(cc.log("\u627e\u5230AnimalColorBtn\u7ec4\u4ef6\uff0c\u5f00\u59cb\u6d4b\u8bd5..."),o.testButtonFunction?o.testButtonFunction():cc.error("testButtonFunction\u65b9\u6cd5\u4e0d\u5b58\u5728"),this.schedule(function(){cc.log("========== \u81ea\u52a8\u6d4b\u8bd5 =========="),o&&o.testButtonFunction&&o.testButtonFunction()},5)):cc.error("\u672a\u627e\u5230AnimalColorBtn\u7ec4\u4ef6")}else cc.error("\u672a\u627e\u5230AnimalColorBtn\u8282\u70b9")}else cc.error("\u672a\u627e\u5230GameUI\u8282\u70b9")},e.prototype.manualTest=function(){this.runTests()},a([s],e)}(cc.Component));o.default=c,cc._RF.pop()},{}],AnimalColorBtn:[function(t,e,o){"use strict";cc._RF.push(e,"a496aKpVvBNSYlekW4wFu7V","AnimalColorBtn");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../../uis/GameUI"),s=t("../../items/AnimalItem"),c=cc._decorator,l=c.ccclass,u=c.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.colorBtn=null,e.btnLabel=null,e.btnIcon=null,e.isColorMode=!0,e.originalAtlases=new Map,e.colorModeIcon=null,e.multiColorModeIcon=null,e.cachedCatColorFrames=null,e.colorAtlasMappings={cat:"cat_color",panda:"cat_color",pig:"cat_color",rabbit:"cat_color",sheep:"cat_color"},e}return i(e,t),e.prototype.start=function(){var t=this;cc.log("AnimalColorBtn: start() \u5f00\u59cb\u6267\u884c"),cc.log("AnimalColorBtn: colorBtn\u7ed1\u5b9a\u72b6\u6001:",!!this.colorBtn),cc.log("AnimalColorBtn: btnLabel\u7ed1\u5b9a\u72b6\u6001:",!!this.btnLabel),cc.log("AnimalColorBtn: btnIcon\u7ed1\u5b9a\u72b6\u6001:",!!this.btnIcon),this.btnIcon&&cc.log("AnimalColorBtn: btnIcon\u5f53\u524dspriteFrame:",this.btnIcon.spriteFrame?this.btnIcon.spriteFrame.name:"null"),this.loadButtonIcons(),this.updateBtnUI(),cc.log("AnimalColorBtn: \u521d\u59cb\u5316\u65f6\u5e94\u7528\u7eaf\u8272\u6a21\u5f0f"),this.scheduleOnce(function(){t.switchAnimalColors()},.5),this.colorBtn?(cc.log("AnimalColorBtn: \u6309\u94ae\u7ec4\u4ef6\u5df2\u7ed1\u5b9a\uff0c\u4f7f\u7528\u9884\u5236\u4f53\u914d\u7f6e\u7684\u70b9\u51fb\u4e8b\u4ef6"),cc.log("AnimalColorBtn: \u6309\u94ae\u53ef\u4ea4\u4e92\u72b6\u6001:",this.colorBtn.interactable),cc.log("AnimalColorBtn: \u6309\u94ae\u542f\u7528\u72b6\u6001:",this.colorBtn.enabled)):cc.warn("AnimalColorBtn: colorBtn\u672a\u6b63\u786e\u7ed1\u5b9a")},e.prototype.loadButtonIcons=function(){var t=this;cc.log("AnimalColorBtn: \u5f00\u59cb\u52a0\u8f7d\u6309\u94ae\u56fe\u6807");var e=0;cc.resources.load("other/\u7eaf\u8272\u6a21\u5f0f",cc.SpriteFrame,function(o,n){!o&&n?(t.colorModeIcon=n,cc.log("AnimalColorBtn: \u7eaf\u8272\u6a21\u5f0f\u56fe\u6807\u52a0\u8f7d\u6210\u529f\uff0c\u540d\u79f0:",n.name)):cc.warn("AnimalColorBtn: \u7eaf\u8272\u6a21\u5f0f\u56fe\u6807\u52a0\u8f7d\u5931\u8d25:",o),e++,cc.log("AnimalColorBtn: \u56fe\u6807\u52a0\u8f7d\u8fdb\u5ea6 "+e+"/2"),e>=2&&(cc.log("AnimalColorBtn: \u6240\u6709\u56fe\u6807\u52a0\u8f7d\u5b8c\u6210\uff0c\u66f4\u65b0\u6309\u94ae\u56fe\u6807"),t.updateBtnIcon())}),cc.resources.load("other/\u591a\u8272\u6a21\u5f0f",cc.SpriteFrame,function(o,n){!o&&n?(t.multiColorModeIcon=n,cc.log("AnimalColorBtn: \u591a\u8272\u6a21\u5f0f\u56fe\u6807\u52a0\u8f7d\u6210\u529f\uff0c\u540d\u79f0:",n.name)):cc.warn("AnimalColorBtn: \u591a\u8272\u6a21\u5f0f\u56fe\u6807\u52a0\u8f7d\u5931\u8d25:",o),e++,cc.log("AnimalColorBtn: \u56fe\u6807\u52a0\u8f7d\u8fdb\u5ea6 "+e+"/2"),e>=2&&(cc.log("AnimalColorBtn: \u6240\u6709\u56fe\u6807\u52a0\u8f7d\u5b8c\u6210\uff0c\u66f4\u65b0\u6309\u94ae\u56fe\u6807"),t.updateBtnIcon())})},e.prototype.onClick=function(){cc.log("AnimalColorBtn: ========== \u6309\u94ae\u88ab\u70b9\u51fb =========="),cc.log("AnimalColorBtn: \u70b9\u51fb\u524d\u72b6\u6001 - isColorMode: "+this.isColorMode),cc.log("AnimalColorBtn: \u70b9\u51fb\u524d\u72b6\u6001 - \u6309\u94ae\u53ef\u4ea4\u4e92: "+(this.colorBtn?this.colorBtn.interactable:"colorBtn\u672a\u7ed1\u5b9a"));var t=r.default.inst;t&&(cc.log("AnimalColorBtn: \u6e38\u620f\u6682\u505c\u72b6\u6001: "+t.is_game_pause),cc.log("AnimalColorBtn: \u6e38\u620f\u8fdb\u884c\u72b6\u6001: "+t.is_game_playing),t.is_game_pause)?cc.warn("AnimalColorBtn: \u6e38\u620f\u5df2\u6682\u505c\uff0c\u5ffd\u7565\u70b9\u51fb"):!this.colorBtn||this.colorBtn.interactable?(this.isColorMode=!this.isColorMode,cc.log("AnimalColorBtn: \u5207\u6362\u540e\u6a21\u5f0f: "+(this.isColorMode?"\u7eaf\u8272":"\u591a\u8272")),this.updateBtnUI(),this.forceUpdateIcon(),this.switchAnimalColors(),this.ensureButtonInteractable(),cc.log("AnimalColorBtn: ========== \u6309\u94ae\u70b9\u51fb\u5904\u7406\u5b8c\u6210 ==========")):cc.warn("AnimalColorBtn: \u6309\u94ae\u5f53\u524d\u4e0d\u53ef\u4ea4\u4e92\uff0c\u5ffd\u7565\u70b9\u51fb")},e.prototype.switchAnimalColors=function(){var t=this,e=r.default.inst;e&&e.cat_info_list&&e.cat_info_list.forEach(function(e){var o=e.cat.getComponent(s.default);o&&(t.isColorMode?t.switchToColorMode(o):t.restoreOriginalAtlas(o))})},e.prototype.switchToColorMode=function(t){var e=this;if(!t.isAtlasReady())return cc.warn("\u52a8\u7269\u56fe\u96c6\u8fd8\u672a\u52a0\u8f7d\u5b8c\u6210\uff0c\u5ef6\u8fdf\u91cd\u8bd5"),void this.scheduleOnce(function(){e.switchToColorMode(t)},.1);var o=t.getCurrentAtlasName();cc.log("\u539f\u59cb\u52a8\u7269\u7c7b\u578b: "+o+" -> \u5c06\u53d8\u6210\u7eaf\u8272\u5c0f\u732b"),this.originalAtlases.has(t)||this.originalAtlases.set(t,t.getCurrentAtlas()),this.loadCatColorFrames(t)},e.prototype.loadCatColorFrames=function(t){var e=this;if(this.cachedCatColorFrames&&this.cachedCatColorFrames.length>0)return cc.log("\u4f7f\u7528\u7f13\u5b58\u7684\u7eaf\u8272\u5c0f\u732b\u56fe\u7247\u5e27"),void this.createCatColorAnimation(t,this.cachedCatColorFrames);for(var o=[],n=1;n<=9;n++){var i="atlas/animals_singlecolor/cat_color/"+n;o.push(this.loadSingleFrame(i))}o.push(this.loadSingleFrame("atlas/animals_singlecolor/cat_color/hit")),Promise.all(o).then(function(o){var n=o.filter(function(t){return null!==t});n.length>0&&t.isValid?(e.cachedCatColorFrames=n,e.createCatColorAnimation(t,n),cc.log("\u6210\u529f\u52a0\u8f7d\u5e76\u7f13\u5b58\u7eaf\u8272\u5c0f\u732b\u56fe\u7247\uff0c\u5171"+n.length+"\u5e27")):cc.error("\u65e0\u6cd5\u52a0\u8f7d\u7eaf\u8272\u5c0f\u732b\u56fe\u7247")})},e.prototype.loadSingleFrame=function(t){return new Promise(function(e){cc.resources.load(t,cc.SpriteFrame,function(o,n){o?(console.warn("\u52a0\u8f7d\u5e27\u5931\u8d25: "+t,o),e(null)):e(n)})})},e.prototype.createCatColorAnimation=function(t,e){if(t.animalNode){var o=t.animalNode.getComponent(cc.Animation);if(o){o.removeClip("anim_idle"),o.removeClip("anim_collision");var n=e.slice(0,9);if(n.length>0){var i=cc.AnimationClip.createWithSpriteFrames(n,6);i.name="anim_idle",i.wrapMode=cc.WrapMode.Loop,o.addClip(i),o.play("anim_idle")}this.createCatColorCollisionAnimation(o,e)}}},e.prototype.createCatColorCollisionAnimation=function(t,e){for(var o=[],n=0;n<Math.min(2,e.length);n++)o.push(e[n]);var i=e[e.length-1];if(i)for(n=0;n<8;n++)o.push(i);for(n=0;n<Math.min(2,e.length);n++)o.push(e[n]);if(o.length>0){var a=cc.AnimationClip.createWithSpriteFrames(o,6);a.name="anim_collision",a.wrapMode=cc.WrapMode.Normal,a.speed=1,t.addClip(a)}},e.prototype.restoreOriginalAtlas=function(t){var e=this.originalAtlases.get(t);e&&t.isValid?(cc.log("\u6062\u590d\u52a8\u7269\u539f\u59cb\u56fe\u96c6: "+e.name),t.setAtlas(e)):cc.warn("\u65e0\u6cd5\u6062\u590d\u539f\u59cb\u56fe\u96c6\uff0c\u539f\u59cb\u56fe\u96c6\u672a\u627e\u5230\u6216\u52a8\u7269\u65e0\u6548")},e.prototype.getColorAtlasName=function(t){var e=t.replace(".plist","");return this.colorAtlasMappings[e]||null},e.prototype.updateBtnUI=function(){cc.log("AnimalColorBtn: updateBtnUI() \u5f00\u59cb"),this.colorBtn&&cc.log("AnimalColorBtn: \u6309\u94ae\u72b6\u6001 - \u53ef\u4ea4\u4e92: "+this.colorBtn.interactable+", \u542f\u7528: "+this.colorBtn.enabled),this.btnLabel&&(this.btnLabel.string=this.isColorMode?"\u6062\u590d\u539f\u8272":"\u7eaf\u8272\u6a21\u5f0f"),this.updateBtnIcon(),this.ensureButtonInteractable(),cc.log("AnimalColorBtn: updateBtnUI() \u5b8c\u6210")},e.prototype.updateBtnIcon=function(){var t=this;if(cc.log("AnimalColorBtn: updateBtnIcon() \u5f00\u59cb\u6267\u884c\uff0c\u5f53\u524d\u6a21\u5f0f: "+(this.isColorMode?"\u7eaf\u8272":"\u591a\u8272")),this.btnIcon){if(cc.log("AnimalColorBtn: btnIcon\u5f53\u524dspriteFrame:",this.btnIcon.spriteFrame?this.btnIcon.spriteFrame.name:"null"),cc.log("AnimalColorBtn: colorModeIcon\u72b6\u6001:",!!this.colorModeIcon),cc.log("AnimalColorBtn: multiColorModeIcon\u72b6\u6001:",!!this.multiColorModeIcon),this.isColorMode)if(this.multiColorModeIcon){var e=this.btnIcon.spriteFrame;this.btnIcon.spriteFrame=this.multiColorModeIcon,cc.log("AnimalColorBtn: \u56fe\u6807\u5df2\u66f4\u65b0 "+(e?e.name:"null")+" -> "+this.multiColorModeIcon.name),cc.log("AnimalColorBtn: \u663e\u793a\u591a\u8272\u6a21\u5f0f\u56fe\u6807\uff08\u5f53\u524d\u4e3a\u7eaf\u8272\u6a21\u5f0f\uff09")}else cc.warn("AnimalColorBtn: \u591a\u8272\u6a21\u5f0f\u56fe\u6807\u672a\u52a0\u8f7d\uff0c\u65e0\u6cd5\u5207\u6362\u56fe\u6807");else this.colorModeIcon?(e=this.btnIcon.spriteFrame,this.btnIcon.spriteFrame=this.colorModeIcon,cc.log("AnimalColorBtn: \u56fe\u6807\u5df2\u66f4\u65b0 "+(e?e.name:"null")+" -> "+this.colorModeIcon.name),cc.log("AnimalColorBtn: \u663e\u793a\u7eaf\u8272\u6a21\u5f0f\u56fe\u6807\uff08\u5f53\u524d\u4e3a\u591a\u8272\u6a21\u5f0f\uff09")):cc.warn("AnimalColorBtn: \u7eaf\u8272\u6a21\u5f0f\u56fe\u6807\u672a\u52a0\u8f7d\uff0c\u65e0\u6cd5\u5207\u6362\u56fe\u6807");this.btnIcon.node&&(this.btnIcon.node.active=!1,this.btnIcon.node.active=!0),this.scheduleOnce(function(){t.forceUpdateIcon()},.1)}else cc.warn("AnimalColorBtn: btnIcon\u672a\u8bbe\u7f6e")},e.prototype.forceUpdateIcon=function(){if(this.btnIcon){cc.log("AnimalColorBtn: \u5f3a\u5236\u66f4\u65b0\u56fe\u6807");var t=this.isColorMode?this.multiColorModeIcon:this.colorModeIcon;t&&(this.btnIcon.spriteFrame=t,cc.log("AnimalColorBtn: \u5f3a\u5236\u8bbe\u7f6e\u56fe\u6807\u4e3a: "+t.name),this.btnIcon.markForRender(!0))}},e.prototype.ensureButtonInteractable=function(){if(this.colorBtn){var t=this.colorBtn.interactable;this.colorBtn.interactable=!0,this.colorBtn.enabled=!0,cc.log("AnimalColorBtn: \u6309\u94ae\u72b6\u6001\u68c0\u67e5 - \u4e4b\u524d\u53ef\u4ea4\u4e92: "+t+", \u73b0\u5728\u53ef\u4ea4\u4e92: "+this.colorBtn.interactable),t||cc.warn("AnimalColorBtn: \u6309\u94ae\u4e4b\u524d\u88ab\u7981\u7528\uff0c\u73b0\u5df2\u91cd\u65b0\u542f\u7528")}},e.prototype.resetColorMode=function(){cc.log("AnimalColorBtn: \u91cd\u7f6e\u989c\u8272\u6a21\u5f0f\u72b6\u6001\u5230\u521d\u59cb\u7eaf\u8272\u6a21\u5f0f"),this.isColorMode=!0,this.originalAtlases.clear(),this.updateBtnUI(),this.switchAnimalColors()},e.prototype.clearDataKeepMode=function(){cc.log("AnimalColorBtn: \u6e05\u7406\u6570\u636e\u4f46\u4fdd\u6301\u5f53\u524d\u989c\u8272\u6a21\u5f0f\u72b6\u6001"),cc.log("AnimalColorBtn: \u5f53\u524d\u6a21\u5f0f:",this.isColorMode?"\u7eaf\u8272\u6a21\u5f0f":"\u591a\u8272\u6a21\u5f0f"),this.originalAtlases.clear(),this.updateBtnUI(),this.switchAnimalColors()},e.prototype.testButtonFunction=function(){cc.log("AnimalColorBtn: \u6d4b\u8bd5\u6309\u94ae\u529f\u80fd"),cc.log("AnimalColorBtn: \u5f53\u524d\u72b6\u6001:",this.isColorMode?"\u7eaf\u8272\u6a21\u5f0f":"\u591a\u8272\u6a21\u5f0f"),cc.log("AnimalColorBtn: colorModeIcon:",!!this.colorModeIcon),cc.log("AnimalColorBtn: multiColorModeIcon:",!!this.multiColorModeIcon),cc.log("AnimalColorBtn: btnIcon:",!!this.btnIcon),this.btnIcon&&this.btnIcon.spriteFrame&&cc.log("AnimalColorBtn: \u5f53\u524d\u56fe\u6807:",this.btnIcon.spriteFrame.name),this.onClick()},e.prototype.onDestroy=function(){this.cachedCatColorFrames&&this.cachedCatColorFrames.clear()},a([u(cc.Button)],e.prototype,"colorBtn",void 0),a([u(cc.Label)],e.prototype,"btnLabel",void 0),a([u(cc.Sprite)],e.prototype,"btnIcon",void 0),a([l],e)}(cc.Component);o.default=p,cc._RF.pop()},{"../../items/AnimalItem":"AnimalItem","../../uis/GameUI":"GameUI"}],AnimalColorTransition:[function(t,e,o){"use strict";cc._RF.push(e,"8e949aGgC1GrI0on1x5mbpw","AnimalColorTransition");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.transitionAtlasDirectory="atlas/animals_transition/",e.transitionAtlas=null,e.frameCount=10,e.isPlaying=!1,e}return i(e,t),e.prototype.playColorTransition=function(t,e,o){var n=this;if(!this.isPlaying&&t&&t.isValid){this.isPlaying=!0;var i=this.getAnimalTypeFromAtlas(t.getCurrentAtlasName());if(!i)return cc.warn("\u65e0\u6cd5\u8bc6\u522b\u52a8\u7269\u7c7b\u578b\uff0c\u8df3\u8fc7\u6362\u8272\u52a8\u753b"),void this.completeTransition(t,e,o);this.loadTransitionAtlas(i,function(i){i?n.playTransitionAnimation(t,i,function(){n.completeTransition(t,e,o)}):n.completeTransition(t,e,o)})}},e.prototype.getAnimalTypeFromAtlas=function(t){if(!t)return null;for(var e=t.replace(".plist","").replace("_color",""),o=0,n=["cat","panda","pig","rabbit","sheep"];o<n.length;o++){var i=n[o];if(e.includes(i))return i}return null},e.prototype.loadTransitionAtlas=function(t,e){var o=this,n=""+this.transitionAtlasDirectory+t+"_transition";cc.resources.load(n,cc.SpriteAtlas,function(t,i){t?(cc.warn("\u6362\u8272\u8fc7\u6e21\u56fe\u96c6\u52a0\u8f7d\u5931\u8d25: "+n,t),e(null)):(o.transitionAtlas=i,e(i))})},e.prototype.playTransitionAnimation=function(t,e,o){var n=this,i=t.animalNode;if(i){var a=i.getComponent(cc.Animation);if(a){a.stop();var r=this.createTransitionSpriteFrames(e);if(0===r.length)return cc.warn("\u6362\u8272\u8fc7\u6e21\u5e27\u521b\u5efa\u5931\u8d25"),void o();a.removeClip("anim_color_transition");var s=cc.AnimationClip.createWithSpriteFrames(r,6);s.name="anim_color_transition",s.wrapMode=cc.WrapMode.Normal,s.speed=1,a.addClip(s);var c=function(t,e){"anim_color_transition"===e.name&&(a.off("stop",c,n),o())};a.on("stop",c,this),a.play("anim_color_transition")}else o()}else o()},e.prototype.createTransitionSpriteFrames=function(t){for(var e=[],o=1;o<=this.frameCount;o++){for(var n=null,i=0,a=[o.toString(),o+".PNG",o+".png"];i<a.length;i++){var r=a[i];if(n=t.getSpriteFrame(r))break}n&&e.push(n)}return e},e.prototype.completeTransition=function(t,e,o){this.isPlaying=!1,e&&t.setAtlas(e),o&&o()},e.prototype.isTransitionPlaying=function(){return this.isPlaying},a([c({displayName:"\u6362\u8272\u8fc7\u6e21\u56fe\u96c6\u76ee\u5f55",tooltip:"\u6362\u8272\u8fc7\u6e21\u52a8\u753b\u56fe\u96c6\u6240\u5728\u7684\u76ee\u5f55\u8def\u5f84"})],e.prototype,"transitionAtlasDirectory",void 0),a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{}],AnimalItemFrames:[function(t,e,o){"use strict";cc._RF.push(e,"090de23HhlFFZq0G5qF3MfC","AnimalItemFrames");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.animalNode=null,e.starNode=null,e.animalType="cat",e.isColorMode=!1,e.frameCount=9,e.currentFrames=[],e}return i(e,t),e.prototype.start=function(){this.loadAnimalFrames()},e.prototype.loadAnimalFrames=function(){for(var t=this,e=this.getBasePath(),o=[],n=1;n<=this.frameCount;n++){var i=e+"/"+n;o.push(this.loadSingleFrame(i))}var a=e+"/hit";o.push(this.loadSingleFrame(a)),Promise.all(o).then(function(e){t.currentFrames=e.filter(function(t){return null!==t}),t.createAnimation()})},e.prototype.getBasePath=function(){var t=this.isColorMode?"color":"normal";return"animals/frames/"+this.animalType+"/"+t},e.prototype.loadSingleFrame=function(t){return new Promise(function(e){cc.resources.load(t,cc.SpriteFrame,function(o,n){o?(console.warn("\u52a0\u8f7d\u5e27\u5931\u8d25: "+t,o),e(null)):e(n)})})},e.prototype.createAnimation=function(){if(this.animalNode&&0!==this.currentFrames.length){var t=this.animalNode.getComponent(cc.Animation);if(t){t.removeClip("anim_idle"),t.removeClip("anim_collision");var e=this.currentFrames.slice(0,this.frameCount);if(e.length>0){var o=cc.AnimationClip.createWithSpriteFrames(e,6);o.name="anim_idle",o.wrapMode=cc.WrapMode.Loop,t.addClip(o),t.play("anim_idle")}this.createCollisionAnimation(t)}}},e.prototype.createCollisionAnimation=function(t){var e=[],o=this.currentFrames[this.frameCount];if(o)for(var n=0;n<8;n++)e.push(o);for(n=0;n<2&&n<this.currentFrames.length;n++)e.push(this.currentFrames[n]);if(e.length>0){var i=cc.AnimationClip.createWithSpriteFrames(e,6);i.name="anim_collision",i.wrapMode=cc.WrapMode.Normal,i.speed=1,t.addClip(i)}},e.prototype.switchColorMode=function(t){this.isColorMode!==t&&(this.isColorMode=t,this.loadAnimalFrames())},e.prototype.switchAnimalType=function(t){this.animalType!==t&&(this.animalType=t,this.loadAnimalFrames())},e.prototype.playCollisionAnimation=function(){var t=this,e=this.animalNode.getComponent(cc.Animation);e&&(e.on("stop",this.onCollisionAnimationStop,this),e.play("anim_collision"),this.starNode&&(this.starNode.active=!0,cc.tween(this.starNode).to(1,{angle:720}).call(function(){t.starNode.active=!1,t.starNode.angle=0}).start()))},e.prototype.onCollisionAnimationStop=function(){var t=this.animalNode.getComponent(cc.Animation);t&&(t.off("stop",this.onCollisionAnimationStop,this),t.play("anim_idle"))},e.prototype.loadAnimalRandomly=function(){var t=["cat","panda","pig","rabbit","sheep"],e=t[Math.floor(Math.random()*t.length)];this.switchAnimalType(e)},a([c(cc.Node)],e.prototype,"animalNode",void 0),a([c(cc.Node)],e.prototype,"starNode",void 0),a([c({displayName:"\u52a8\u7269\u7c7b\u578b",tooltip:"\u52a8\u7269\u7684\u7c7b\u578b\u540d\u79f0\uff0c\u5982\uff1acat, panda, rabbit\u7b49"})],e.prototype,"animalType",void 0),a([c({displayName:"\u662f\u5426\u5f69\u8272\u6a21\u5f0f",tooltip:"\u662f\u5426\u4f7f\u7528\u5f69\u8272\u7248\u672c\u7684\u56fe\u7247"})],e.prototype,"isColorMode",void 0),a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{}],AnimalItem:[function(t,e,o){"use strict";cc._RF.push(e,"9521e7ENxFDNrM1fFO4+GL3","AnimalItem");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../uis/GameUI"),s=t("../utils/manager/PrefabUtil"),c=cc._decorator,l=c.ccclass,u=c.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.atlas=null,e.animalNode=null,e.starNode=null,e.atlasDirectory="atlas/animals/",e.frameCount=9,e.isAtlasLoaded=!1,e.pendingColorSwitch=null,e}return i(e,t),e.prototype.start=function(){},e.prototype.onEnable=function(){this.loadAnimalRandomly()},e.prototype.loadAnimalRandomly=function(){var t=this;cc.resources.loadDir(this.atlasDirectory,function(e,o){if(e)console.error("Failed to load directory:",e);else{var n=o.filter(function(t){return t instanceof cc.SpriteAtlas});0!==n.length?(t.atlas=n[Math.floor(Math.random()*n.length)],t.isAtlasLoaded=!0,t.pendingColorSwitch?(t.setAtlas(t.pendingColorSwitch),t.pendingColorSwitch=null):t.changeCharacterAppearance()):console.error("No SpriteAtlas found in the directory.")}})},e.prototype.loadSpecificAnimal=function(t){var e=this,o=""+this.atlasDirectory+t;cc.resources.load(o,cc.SpriteAtlas,function(t,o){!t&&o&&(e.atlas=o,e.isAtlasLoaded=!0,e.pendingColorSwitch?(e.setAtlas(e.pendingColorSwitch),e.pendingColorSwitch=null):e.changeCharacterAppearance())})},e.prototype.createSpriteFrames=function(){for(var t=[],e=1;e<=this.frameCount;e++){for(var o=null,n=0,i=[e.toString(),e+".PNG",e+".png"];n<i.length;n++){var a=i[n];if(o=this.atlas.getSpriteFrame(a))break}o&&t.push(o)}return t},e.prototype.changeCharacterAppearance=function(){if(this.animalNode){var t=this.animalNode.getComponent(cc.Animation);if(t){t.removeClip("anim_idle"),t.removeClip("anim_collision");var e=this.createSpriteFrames();if(e.length>0){var o=cc.AnimationClip.createWithSpriteFrames(e,6);o.name="anim_idle",o.wrapMode=cc.WrapMode.Loop,t.addClip(o),t.play("anim_idle")}var n=this.createCollisonSpriteFrames();if(n.length>0){var i=cc.AnimationClip.createWithSpriteFrames(n,6);i.name="anim_collision",i.wrapMode=cc.WrapMode.Normal,i.speed=1,t.addClip(i)}}}},e.prototype.createCollisonSpriteFrames=function(){for(var t=[],e=null,o=0,n=["hit","hit.PNG","hit.png"];o<n.length;o++){var i=n[o];if(e=this.atlas.getSpriteFrame(i))break}e&&(t=t.concat.apply(t,Array(8).fill(e)));for(var a=1;a<=2;a++){for(var r=null,s=0,c=[a.toString(),a+".PNG",a+".png"];s<c.length&&(i=c[s],!(r=this.atlas.getSpriteFrame(i)));s++);r&&t.push(r)}return t},e.prototype.playCollisionAnimation=function(){var t=this,e=this.animalNode.getComponent(cc.Animation);e.on("stop",this.callback,this),e.play("anim_collision"),this.starNode&&(this.starNode.active=!0,cc.tween(this.starNode).to(1,{angle:720}).call(function(){t.starNode.active=!1,t.starNode.angle=0}).start())},e.prototype.callback=function(t,e){var o=this.animalNode.getComponent(cc.Animation);"anim_collision"==e.name&&o.play("anim_idle")},e.prototype.onDestroy=function(){console.log("animal item destroy"),this.animalNode.getComponent(cc.Animation).off("stop",this.callback,this)},e.prototype.createTrailEffect=function(){var t=this;this.schedule(function(){var e;if(r.default.inst.movementEffectPool.size()>0)e=r.default.inst.movementEffectPool.get();else{var o=s.default.get("MovementEffect");if(!o)return void console.error("MovementEffect\u9884\u5236\u4f53\u672a\u627e\u5230");e=cc.instantiate(o)}var n=0;r.default.inst.catContainer.addChild(e);var i=t.node.getPosition(),a=t.node.getPosition();switch(t.node.angle){case 0:n=-90,i.sub(new cc.Vec2(0,50),a);break;case 180:n=90,i.add(new cc.Vec2(0,50),a);break;case 90:n=0,i.add(new cc.Vec2(50,0),a);break;case 270:n=180,i.sub(new cc.Vec2(50,0),a)}e.angle=n;var c=t.node.parent.convertToWorldSpaceAR(a);e.setPosition(e.parent.convertToNodeSpaceAR(c)),cc.tween(e).to(1,{opacity:0}).call(function(){e.removeFromParent(),e.opacity=255,r.default.inst.movementEffectPool.put(e)}).start()},.2,3,.2)},e.prototype.getCurrentAtlas=function(){return this.atlas},e.prototype.setAtlas=function(t){t&&(this.isAtlasLoaded?(this.atlas=t,this.changeCharacterAppearance()):this.pendingColorSwitch=t)},e.prototype.getCurrentAtlasName=function(){return this.atlas?this.atlas.name:""},e.prototype.isAtlasReady=function(){return this.isAtlasLoaded&&null!==this.atlas},a([u(cc.Node)],e.prototype,"animalNode",void 0),a([u(cc.Node)],e.prototype,"starNode",void 0),a([l],e)}(cc.Component);o.default=p,cc._RF.pop()},{"../uis/GameUI":"GameUI","../utils/manager/PrefabUtil":"PrefabUtil"}],AnimalYard:[function(t,e,o){"use strict";cc._RF.push(e,"6b952QNOWdHJZb93DSOncEr","AnimalYard");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../../manager/LocalData"),s=cc._decorator,c=s.ccclass,l=s.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.rank=null,e.provinceName=null,e.signboard=null,e.score=null,e.skins=[],e.sign=[],e.userNode=null,e}return i(e,t),e.prototype.onLoad=function(){this.setRandomAnimal(),this.setSignBoard()},e.prototype.setSignBoard=function(){if(this.signboard){var t=this.signboard.getComponent(cc.Sprite);t&&(t.spriteFrame=this.sign[Math.floor(Math.random()*this.sign.length)])}},e.prototype.setRandomAnimal=function(){var t=this,e=this.node.getChildByName("yard");e&&e.children.forEach(function(e){var o=e.getChildByName("animal").getComponent(cc.Sprite);if(o){var n=Math.floor(Math.random()*t.skins.length);o.spriteFrame=t.skins[n]}})},e.prototype.pickUserAnimal=function(){var t=this.node.getChildByName("yard").children.slice(-4);if(t.length>0){var e=Math.floor(Math.random()*t.length);this.userNode=t[e]}},e.prototype.changeSkin=function(){if(r.default.currentSkin&&this.userNode.getChildByName("animal")){var t=this.findSpriteFrameByName(r.default.currentSkin),e=this.userNode.getChildByName("animal").getComponent(cc.Sprite);t&&e?e.spriteFrame=t:console.log("miss user current skin!")}},e.prototype.switchActiveOfPoint=function(){var t=this.userNode.getChildByName("you");t&&(t.active=!t.active);var e=this.userNode.getChildByName("pointYou");e&&(e.active=!e.active)},e.prototype.findSpriteFrameByName=function(t){for(var e=0,o=this.skins;e<o.length;e++){var n=o[e];if(n.name===t)return n}return null},e.prototype.start=function(){},e.prototype.setRank=function(t){this.rank?this.rank.string="\u7b2c"+t+"\u540d":console.error("miss rank property")},e.prototype.setProvinceName=function(t){this.provinceName?this.provinceName.string=t+"\u961f":console.error("miss province name label")},e.prototype.setScore=function(t){this.score?this.score.string=t+"\u5206":console.error("miss score label")},a([l(cc.Label)],e.prototype,"rank",void 0),a([l(cc.Label)],e.prototype,"provinceName",void 0),a([l(cc.Sprite)],e.prototype,"signboard",void 0),a([l(cc.Label)],e.prototype,"score",void 0),a([l([cc.SpriteFrame])],e.prototype,"skins",void 0),a([l([cc.SpriteFrame])],e.prototype,"sign",void 0),a([c],e)}(cc.Component);o.default=u,cc._RF.pop()},{"../../manager/LocalData":"LocalData"}],AudioMgr:[function(t,e,o){"use strict";cc._RF.push(e,"a9130ZRzuZBMbv/u5ULZ2g8","AudioMgr");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=t("./LocalData"),a=cc._decorator,r=a.ccclass,s=(a.property,function(){function t(){}return t.playSound=function(t){var e=this;0!=i.default.yx&&(1==this.sounds.hasOwnProperty(t)?cc.audioEngine.playEffect(this.sounds[t],!1):cc.resources.load("sound/"+t,cc.AudioClip,null,function(o,n){null!=n&&(e.sounds[t]=n,cc.audioEngine.playEffect(n,!1))}))},t.playBgm=function(){var t=this;0!=i.default.yy&&(1==this.sounds.hasOwnProperty(this.bgm_name)?cc.audioEngine.playMusic(this.sounds[this.bgm_name],!0):cc.resources.load("sound/"+this.bgm_name,cc.AudioClip,null,function(e,o){null!=o&&(t.sounds[t.bgm_name]=o,cc.audioEngine.playMusic(o,!0))}))},t.stopBgm=function(){cc.audioEngine.stopMusic()},t.sounds={},t.bgm_name="soundBg",n([r],t)}());o.default=s,cc._RF.pop()},{"./LocalData":"LocalData"}],AudioPath:[function(t,e,o){"use strict";cc._RF.push(e,"f04cdB6JVVLKZ+mGuRwB8Nc","AudioPath"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){}return t.CLICK="click",t.SHEEP="clickSheep",t.LEVELUP="levelUp",t}();o.default=n,cc._RF.pop()},{}],BuffItemUI:[function(t,e,o){"use strict";cc._RF.push(e,"3ee18WrITJCbb7E4irLToSQ","BuffItemUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/UIbase"),s=t("../utils/manager/PrefabUtil"),c=t("../manager/AudioMgr"),l=t("../datas/AudioPath"),u=t("./GameUI"),p=t("../utils/Palt"),f=t("../datas/Constants"),h=cc._decorator,d=h.ccclass,g=h.property,m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.buffItemName=null,e.buffItemSprite=null,e.buffItemLabel=null,e.currentBuffItem=null,e._buffItemBtn=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst||null==this._inst.node){var t=cc.instantiate(s.default.get("BuffItemUI"));this._inst=t.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){},e.prototype.showUI=function(t){this._buffItemBtn=t,this.currentBuffItem=t.identifier,this.onClickAdReward()},e.prototype.updateSpriteFrame=function(t){var e=this;if(this.buffItemSprite){var o="";switch(t){case f.BuffItems.Undo:o="btn_undo";break;case f.BuffItems.Inverse:o="btn_inverse";break;case f.BuffItems.Hint:o="btn_hint";break;case f.BuffItems.SwapAnimal:o="btn_fluctuate";break;case f.BuffItems.AddTime:o="btn_addTime"}cc.resources.load("/other/"+o,cc.SpriteFrame,function(t,o){t?console.log(t.message):e.buffItemSprite.spriteFrame=o})}},e.prototype.updateLabel=function(t){if(this.buffItemLabel){var e="",o="";switch(t){case f.BuffItems.Undo:e="\u5141\u8bb8\u73a9\u5bb6\u5728\u6e38\u620f\u4e2d\u56de\u5230\u4e0a\u4e00\u6b65\u7684\u72b6\u6001\uff0c\u64a4\u9500\u6700\u8fd1\u7684\u4e00\u6b21\u64cd\u4f5c\u3002",o='"\u73a9\u5bb6\u8868\u793a\u975e\u5e38\u540e\u6094"\u3002';break;case f.BuffItems.Inverse:e="\u8fd9\u4e2a\u9053\u5177\u4f1a\u6539\u53d8\u52a8\u7269\u89d2\u8272\u7684\u671d\u5411.",o='"\u6709\u65f6\u5019\u4e0d\u662f\u6211\u4eec\u4e0d\u52aa\u529b\uff0c\u53ea\u662f\u65b9\u5411\u9519\u4e86\u3002"';break;case f.BuffItems.Hint:e="\u8fd9\u4e2a\u9053\u5177\u4f1a\u5728\u6e38\u620f\u4e2d\u7ed9\u51fa\u63d0\u793a\uff0c\u663e\u793a\u5f53\u524d\u53ef\u4ee5\u8fdb\u884c\u64cd\u4f5c\u7684\u52a8\u7269\u3002",o='"\u4eba\u751f\u554a\u6709\u65f6\u5019\u5c31\u662f\u7f3a\u5c11\u4e00\u70b9\u70b9\u63d0\u793a\u3002"';break;case f.BuffItems.SwapAnimal:e="\u8fd9\u4e2a\u9053\u5177\u4f1a\u968f\u673a\u66f4\u6362\u6e38\u620f\u4e2d\u7684\u52a8\u7269.",o='"\u4e5f\u8bb8\u770b\u8d77\u6765\u66f4\u8212\u670d\u4e00\u4e9b\uff1f"';break;case f.BuffItems.AddTime:e="\u8fd9\u4e2a\u9053\u5177\u4f1a\u589e\u52a0\u6e38\u620f\u7684\u5269\u4f59\u65f6\u95f4.",o='"\u53c8\u6709\u65f6\u95f4\u601d\u8003\u4e86\uff01"'}this.buffItemLabel.string=e+"<br/><b>"+o+"</b>"}},e.prototype.onShow=function(){},e.prototype.onHide=function(){},e.prototype.onClickClose=function(){c.default.playSound(l.default.CLICK),u.default.inst.is_game_pause=!1,this.hideUI()},e.prototype.onClickAdReward=function(){var t=this;p.default.showRewardVideo(function(e){e&&(u.default.inst.useBuffItem(t._buffItemBtn),t._buffItemBtn.init()),u.default.inst.is_game_pause=!1})},a([g(cc.Label)],e.prototype,"buffItemName",void 0),a([g(cc.Sprite)],e.prototype,"buffItemSprite",void 0),a([g(cc.RichText)],e.prototype,"buffItemLabel",void 0),a([g],e.prototype,"_buffItemBtn",void 0),o=a([d],e)}(r.default);o.default=m,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/Constants":"Constants","../manager/AudioMgr":"AudioMgr","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./GameUI":"GameUI"}],CatInfo:[function(t,e,o){"use strict";cc._RF.push(e,"9edcfAaGp9MnJXof4ohcgbx","CatInfo"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){this.now_grids=[],this.dir=1,this.cat=null,this.last_grids=[]}return t.prototype.push_last=function(){this.last_grids.push(this.now_grids.concat())},t.prototype.revoke=function(){this.now_grids=this.last_grids.pop()},t}();o.default=n,cc._RF.pop()},{}],CatItem:[function(t,e,o){"use strict";cc._RF.push(e,"1788bRJJSFFxqDVWEjr8ShQ","CatItem");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../manager/DataMgr"),s=cc._decorator,c=s.ccclass,l=s.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.cat=null,e.catSprite=null,e.state=0,e}return i(e,t),e.prototype.start=function(){var t=this;this.scheduleOnce(function(){t.play(1)},5)},e.prototype.onEnable=function(){var t=this,e=r.DataMgr.ins.getRandomId();cc.resources.load("/cat/cat_skin_"+e,cc.SpriteFrame,function(e,o){t.catSprite.spriteFrame=o})},e.prototype.play=function(t){1==t&&cc.tween(this.cat).to(1,{scaleX:1.2}).to(1,{scaleX:1}).union().repeat(1e6).start()},a([l(cc.Node)],e.prototype,"cat",void 0),a([l(cc.Sprite)],e.prototype,"catSprite",void 0),a([c],e)}(cc.Component);o.default=u,cc._RF.pop()},{"../manager/DataMgr":"DataMgr"}],ChangeGameBgUI:[function(t,e,o){"use strict";cc._RF.push(e,"58eefbmOaBF2aw6qPwjcAfL","ChangeGameBgUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../datas/AudioPath"),s=t("../manager/AudioMgr"),c=t("../manager/LocalData"),l=t("../utils/manager/PrefabUtil"),u=t("../utils/Palt"),p=t("../utils/UIbase"),f=t("./GameUI"),h=t("./TipsUI"),d=cc._decorator,g=d.ccclass,m=d.property,y=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.btn_conatiner=null,e}var o;return i(e,t),o=e,e.prototype.start=function(){},Object.defineProperty(e,"inst",{get:function(){if(null==this._inst||null==this._inst.node){var t=l.default.get("ChangeGameBgUI");if(!t)return console.error("ChangeGameBgUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.onShow=function(){this.updateBg()},e.prototype.updateBg=function(){this.btn_conatiner&&this.btn_conatiner.children.forEach(function(t){t.name!=c.default.GameBgId?t.children.forEach(function(t){t.active=!1}):t.name==c.default.GameBgId&&t.children.forEach(function(t){t.active=!0})})},e.prototype.onHide=function(){u.default.hideBanner()},e.prototype.onClickClose=function(){s.default.playSound(r.default.CLICK),f.default.inst.is_game_pause=!1,this.hideUI()},e.prototype.onClickChangeBgBtn=function(t,e){c.default.GameBgId==e?h.default.inst.showTips("\u5df2\u7ecf\u662f\u5f53\u524d\u7684\u80cc\u666f"):(c.default.GameBgId=e,this.updateBg(),this.updateGameBg(),h.default.inst.showTips("\u66f4\u6362\u80cc\u666f\u6210\u529f"))},e.prototype.updateGameBg=function(){f.default.inst.updateBg()},a([m(cc.Node)],e.prototype,"btn_conatiner",void 0),o=a([g],e)}(p.default);o.default=y,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./GameUI":"GameUI","./TipsUI":"TipsUI"}],ChooseAreaUI:[function(t,e,o){"use strict";cc._RF.push(e,"c2f6b+D/GRPGapoD3FfbXMh","ChooseAreaUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../manager/LocalData"),s=t("../utils/UIbase"),c=t("../utils/manager/PrefabUtil"),l=t("../datas/provincesData"),u=cc._decorator,p=u.ccclass,f=u.property,h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.scrollViewContent=null,e.provinceBtnPrefab=null,e.currentLabel=null,e.btnScript=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=c.default.get("ChooseAreaUI");if(!t)return console.error("ChooseAreaUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.onLoad=function(){var t=this;this.changeLabel(),this.spawnItems(),cc.systemEvent.on("provinceSelect",function(e){null==t.btnScript?(t.btnScript=e,t.btnScript.updateBg()):t.btnScript!==e&&(t.btnScript.updateBg(),e.updateBg(),t.btnScript=e),t.changeLabel()})},e.prototype.showUI=function(e){t.prototype.showUI.call(this,e)},e.prototype.changeLabel=function(){""!==r.default.userRegional&&(this.currentLabel.string="\u5f53\u524d\u6240\u5728\u5730\uff1a "+l.default.provinces[r.default.userRegional].name)},e.prototype.spawnItems=function(){for(var t=l.default.getProvinceRank(),e=0;e<t.length;e++){var o=cc.instantiate(this.provinceBtnPrefab),n=o.getComponent("provinceBtn");n.updateUi(t[e].name,t[e].id),r.default.userRegional==t[e].id&&(n.updateBg(),this.btnScript=n),this.scrollViewContent.addChild(o)}},a([f(cc.Node)],e.prototype,"scrollViewContent",void 0),a([f(cc.Prefab)],e.prototype,"provinceBtnPrefab",void 0),a([f(cc.Label)],e.prototype,"currentLabel",void 0),o=a([p],e)}(s.default);o.default=h,cc._RF.pop()},{"../datas/provincesData":"provincesData","../manager/LocalData":"LocalData","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],ComboUI:[function(t,e,o){"use strict";cc._RF.push(e,"5a97eULvTRAKI8ytkHF2O/J","ComboUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=function(t){function e(){var e=t.call(this)||this;return e.bar=null,e.label=null,e.count=null,e.tip=null,e.progressContainer=null,e.comboCount=0,e.tween=null,e}var o;return i(e,t),o=e,e.getInstance=function(){return o.instance||(o.instance=new o),o.instance},e.prototype.start=function(){this.resetUi()},e.prototype.resetUi=function(){this.comboCount=0,this.tween=null,this.bar.progress=1,this.tip&&(this.tip.active=!0),this.progressContainer&&(this.progressContainer.active=!1),this.count&&(this.count.string="3")},e.prototype.updateProgressBar=function(){if(null!=this.tween)return this.comboCount++,3==this.comboCount&&(this.tip&&(this.tip.active=!1),this.progressContainer&&(this.progressContainer.active=!0)),this.updateComboLabel(),this.tween.stop(),void this.resetTimer();this.resetTimer()},e.prototype.resetTimer=function(){var t=this;this.tween=cc.tween(this.bar).to(.2,{progress:1}).to(5,{progress:0}).call(function(){t.resetUi()}).start()},e.prototype.updateComboLabel=function(){this.label&&this.comboCount>=3&&(cc.tween(this.label.node).to(.5,{scaleX:1.1,scaleY:1.2}).to(.5,{scale:1}).start(),this.count&&(this.count.string=this.comboCount.toString(),cc.tween(this.count.node).to(.5,{scaleX:1.5,scaleY:1.7}).to(.5,{scale:1}).start()))},a([c(cc.ProgressBar)],e.prototype,"bar",void 0),a([c(cc.Label)],e.prototype,"label",void 0),a([c(cc.Label)],e.prototype,"count",void 0),a([c(cc.Node)],e.prototype,"tip",void 0),a([c(cc.Node)],e.prototype,"progressContainer",void 0),o=a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{}],Constants:[function(t,e,o){"use strict";cc._RF.push(e,"242aa2+ruZPSr9afjM22mdG","Constants"),Object.defineProperty(o,"__esModule",{value:!0}),o.GameSetting=o.BuffItems=void 0,function(t){t[t.Undo=0]="Undo",t[t.Hint=1]="Hint",t[t.Inverse=2]="Inverse",t[t.AddTime=3]="AddTime",t[t.SwapAnimal=4]="SwapAnimal"}(o.BuffItems||(o.BuffItems={})),function(t){t[t.addTImeReward=1]="addTImeReward",t[t.hintReward=1]="hintReward",t[t.undoReward=1]="undoReward",t[t.inverseReward=1]="inverseReward",t[t.SwapAnimalReward=1]="SwapAnimalReward"}(o.GameSetting||(o.GameSetting={})),cc._RF.pop()},{}],DataMgr:[function(t,e,o){"use strict";cc._RF.push(e,"9343e9X6WRFjKWwlU9WotH2","DataMgr"),Object.defineProperty(o,"__esModule",{value:!0}),o.DataMgr=void 0;var n=function(){function t(){this.load()}return Object.defineProperty(t,"ins",{get:function(){return this._ins=this._ins||new t,this._ins},enumerable:!1,configurable:!0}),t.prototype.load=function(){var t=localStorage.getItem("local_data_json");this.data=t?JSON.parse(t):{skins:[1],unlockSkinList:[1]}},t.prototype.save=function(){var t=JSON.stringify(this.data);localStorage.setItem("local_data_json",t)},t.prototype.setSkin=function(t){var e=this.data.skins.indexOf(t);-1!=e?this.data.skins.splice(e,1):this.data.skins.push(t),this.data.skins.length<=0&&this.data.skins.push(1),this.save()},t.prototype.isUnlockSkin=function(t){return this.data.unlockSkinList.includes(t)},t.prototype.unlockSkin=function(t){this.data.unlockSkinList.includes(t)||(this.data.unlockSkinList.push(t),this.save())},t.prototype.isUseSkin=function(t){return this.data.skins.includes(t)},t.prototype.getRandomId=function(){var t=Math.floor(Math.random()*this.data.skins.length);return this.data.skins[t]},t}();o.DataMgr=n,cc._RF.pop()},{}],DirectionType:[function(t,e,o){"use strict";cc._RF.push(e,"362c3Nz9OlKhJeeZuD7wD5D","DirectionType"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){}return t.up=1,t.left=2,t.down=3,t.right=4,t}();o.default=n,cc._RF.pop()},{}],En:[function(t,e,o){"use strict";cc._RF.push(e,"878038v3T9Ol6Sq6/yQMpmp","En");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=cc._decorator,a=i.ccclass,r=(i.property,function(){function t(){}return t.en={start:"Start"},n([a],t)}());o.default=r,cc._RF.pop()},{}],FailUI:[function(t,e,o){"use strict";cc._RF.push(e,"9a23242ZdZNUZiEOZr1iRnK","FailUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../datas/AudioPath"),u=t("../manager/AudioMgr"),p=t("../manager/DataMgr"),f=t("../utils/manager/PrefabUtil"),h=t("../utils/Palt"),d=t("../utils/UIbase"),g=t("./GameUI"),m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.cat=null,e.isAutoPlaying=!1,e.autoClickTimer=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=f.default.get("FailUI");if(!t)return console.error("FailUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.showUI=function(e){var o=this;t.prototype.showUI.call(this,e),this.isAutoPlaying=e||!1,console.log("FailUI\u663e\u793a\uff0c\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),this.isAutoPlaying&&(console.log("\u81ea\u52a8\u73a9\u6a21\u5f0f\uff1a3\u79d2\u540e\u81ea\u52a8\u91cd\u8bd5\u5f53\u524d\u5173\u5361"),this.autoClickTimer=setTimeout(function(){console.log("\u81ea\u52a8\u73a9\uff1a\u6a21\u62df\u70b9\u51fb\u91cd\u65b0\u5f00\u59cb\u6309\u94ae"),o.onClickStartGame()},3e3));var n=p.DataMgr.ins.getRandomId();cc.resources.load("/cat/cat_skin_"+n,cc.SpriteFrame,function(t,e){o.cat.spriteFrame=e})},e.prototype.onShow=function(){},e.prototype.onHide=function(){this.autoClickTimer&&(clearTimeout(this.autoClickTimer),this.autoClickTimer=null)},e.prototype.onClickStartGame=function(){console.log("FailUI\u70b9\u51fb\u91cd\u65b0\u5f00\u59cb\uff0c\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),this.hideUI(),g.default.inst.onStartGame(),this.isAutoPlaying&&(console.log("\u81ea\u52a8\u73a9\u6a21\u5f0f\uff1a\u91cd\u65b0\u5f00\u59cb\u540e\u6062\u590d\u81ea\u52a8\u73a9"),setTimeout(function(){g.default.inst&&g.default.inst.is_game_playing&&(g.default.inst.startAutoPlay(),console.log("\u81ea\u52a8\u73a9\u5df2\u6062\u590d"))},1e3)),u.default.playSound(l.default.CLICK)},e.prototype.onClickShare=function(){h.default.shareAppMessage(),u.default.playSound(l.default.CLICK)},a([c(cc.Sprite)],e.prototype,"cat",void 0),o=a([s],e)}(d.default);o.default=m,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../manager/AudioMgr":"AudioMgr","../manager/DataMgr":"DataMgr","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./GameUI":"GameUI"}],GameMain:[function(t,e,o){"use strict";cc._RF.push(e,"95c3dV9pYlF3Zfr9ci4S3y4","GameMain");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("./uis/HomeUI"),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.catContainer=null,e}return i(e,t),e.prototype.onLoad=function(){var t=l.default.inst;t?t.showUI():console.error("HomeUI\u5b9e\u4f8b\u521b\u5efa\u5931\u8d25\uff0c\u65e0\u6cd5\u542f\u52a8\u6e38\u620f")},e.prototype.start=function(){},a([c(cc.Node)],e.prototype,"catContainer",void 0),a([s],e)}(cc.Component);o.default=u,cc._RF.pop()},{"./uis/HomeUI":"HomeUI"}],GameUI:[function(t,e,o){"use strict";cc._RF.push(e,"5e091tclyBN4rfjM7tfE2sT","GameUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../datas/DirectionType"),u=t("../datas/CatInfo"),p=t("../utils/manager/PrefabUtil"),f=t("../utils/util/GameUtil"),h=t("../utils/UIbase"),d=t("./TipsUI"),g=t("../manager/LocalData"),m=t("./WinUI"),y=t("./PauseUI"),_=t("./StepOverUI"),v=t("./TimeOverUI"),b=t("../datas/AudioPath"),I=t("../manager/AudioMgr"),C=t("./ChangeGameBgUI"),P=t("../datas/Constants"),w=t("../datas/mySkinData"),S=t("../utils/Palt"),A=t("../scripts/item/AnimalColorBtn"),U=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.gameBackgroundArray=[],e.isTaskProgressTweening=!1,e.catContainer=null,e.showNode=null,e.gameBg=null,e.maskNode=null,e.weilanNode=null,e.buttonsNode=null,e.selectNode=null,e.timerLabel=null,e.curLevel=null,e.stepLabel=null,e.taskProgressMask=null,e.taskProgressLabel=null,e.comboContainer=null,e.autoPlayBtn=null,e.rewardItemPool=new cc.NodePool,e.movementEffectPool=new cc.NodePool,e.taskProgress=0,e.grid_num_list=[],e.grid_map=new Map,e.cat_info_list=[],e.horizontal_count=20,e.vertical_count=20,e.ww=50,e.old_cat_pos=[],e.is_game_playing=!1,e.is_game_pause=!1,e.stepCount=0,e.isAutoPlaying=!1,e.autoPlayTimer=null,e.isRewardSkinLevel=!1,e.animalColorBtn=null,e.dirs=[1,2,3,4],e.fz_flag=!1,e.xiaochu_flag=!1,e.time=240,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=p.default.get("GameUI");if(!t)return console.error("GameUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){var t=this;setInterval(function(){t.onTimerUpdate()},1e3);for(var e=0;e<5;++e){var o=p.default.get("RewardItem");if(o){var n=cc.instantiate(o);this.rewardItemPool.put(n)}}for(e=0;e<10;++e){var i=p.default.get("MovementEffect");if(i){var a=cc.instantiate(i);this.movementEffectPool.put(a)}}this.checkVipStatus(),cc.systemEvent.on("vipStatusChanged",this.onVipStatusChanged,this)},e.prototype.resetGameData=function(){this.grid_num_list=[],this.grid_map=new Map,this.cat_info_list=[],this.taskProgressLabel&&(this.taskProgressLabel.string="\u8fdb\u5ea6 0%"),this.taskProgressMask&&(this.taskProgressMask.height=0),this.catContainer&&this.catContainer.removeAllChildren(),this.maskNode&&this.catContainer&&(this.maskNode.parent=this.catContainer),this.selectNode&&(this.selectNode.active=!1)},e.prototype.updateBg=function(){var t=this;this.gameBackgroundArray.length>0&&this.gameBackgroundArray.find(function(e){e.name==g.default.GameBgId&&t.gameBg&&(t.gameBg.spriteFrame=e,cc.log("\u80cc\u666f\u5df2\u66f4\u65b0\u4e3a: "+g.default.GameBgId))})},e.prototype.onShow=function(){this.updateBg()},e.prototype.updateStepStr=function(){this.stepLabel&&(this.stepLabel.string="\u221e")},e.prototype.onClickChangeBgBtn=function(){this.is_game_pause=!0,I.default.playSound(b.default.CLICK),C.default.inst.showUI()},e.prototype.onClickAutoPlay=function(){I.default.playSound(b.default.CLICK),this.isAutoPlaying?this.stopAutoPlay():this.startAutoPlay()},e.prototype.addStepCount=function(){this.stepCount+=60,this.updateStepStr(),this.is_game_playing=!0},e.prototype.deleteStepCount=function(){this.stepCount--,this.updateStepStr()},e.prototype.onTimeOver=function(){this.is_game_playing=!1,this.stopAutoPlay(),this.resetComboUi(),v.default.inst.showUI()},e.prototype.onStepOver=function(){0!=this.cat_info_list.length&&(this.is_game_playing=!1,this.stopAutoPlay(),this.resetComboUi(),_.default.inst.showUI())},e.prototype.onStartGame=function(){var t=this;this.is_game_pause=!1,S.default.hidemoban(),this.resetGameData(),this.showUI(),this.updateBg(),this.randomCatInfos(),this.updateUI(),this.scheduleOnce(function(){t.checkShow(),t.taskProgress=t.cat_info_list.length},.1),this.curLevel&&(this.curLevel.string="\u7b2c "+g.default.lv+" \u5173"),this.updateTimeLabel(),this.animalColorBtn&&this.animalColorBtn.clearDataKeepMode()},e.prototype.setLvData=function(){var t=g.default.lv;if(1==t)this.vertical_count=6,this.horizontal_count=6,this.catContainer&&(this.catContainer.scale=1.5),this.time=60;else if(t%2==0){this.isRewardSkinLevel=!1;var e=f.default.randomRange(12,20);this.vertical_count=e,this.horizontal_count=e,this.catContainer&&(this.catContainer.scale=1),this.time=210}else null!=w.default.findMinOrderElementNotInSkins()&&(this.isRewardSkinLevel=!0),this.vertical_count=20,this.horizontal_count=20,this.time=210,this.catContainer&&(this.catContainer.scale=1)},e.prototype.randomCatInfos=function(){this.setLvData(),this.cat_info_list=[];for(var t=0;t<this.vertical_count;t++)for(var e=0;e<this.horizontal_count;e++)this.grid_num_list.push(1e4*t+e),this.grid_map.set(1e4*t+e,0);f.default.randomRangeSort(this.grid_num_list);for(var o=0;0!=this.grid_num_list.length;){f.default.randomRangeSort(this.dirs);var n=!1;if(f.default.randomRange(0,100)<50)for(t=0;t<4;t++){var i=this.grid_num_list[0],a=this.getNextGrid(i,this.dirs[t]);if(-1!=a&&1!=this.grid_map.get(a)){var r=new u.default;r.dir=this.dirs[t],r.now_grids.push(i),r.now_grids.push(a),this.grid_map.set(i,1),this.grid_map.set(a,1),f.default.deleteValue(this.grid_num_list,i),f.default.deleteValue(this.grid_num_list,a),this.cat_info_list.push(r),n=!0;break}}if(0==n&&this.grid_num_list.shift(),++o>1e4)break}1==g.default.lv&&0==this.canAllCatOut()&&this.randomCatInfos(),this.cat_info_list.length<4&&this.randomCatInfos()},e.prototype.getNextGrid=function(t,e){var o=Math.floor(t/1e4),n=t%1e4;e==l.default.up?n--:e==l.default.down?n++:e==l.default.left?o--:e==l.default.right&&o++;var i=1e4*o+n;return 1==this.grid_map.has(i)?i:-1},e.prototype.updateUI=function(){if(this.catContainer){for(var t=0;t<this.cat_info_list.length;t++){var e=this.getCatNode(this.cat_info_list[t]);e.parent=this.catContainer,e.on(cc.Node.EventType.TOUCH_END,this.onClickCatNode,this),this.cat_info_list[t].cat=e,e.opacity=0}0==this.isRewardSkinLevel?this.taskProgressMask&&(this.taskProgressMask.parent.active=!1):this.taskProgressMask&&(this.taskProgressMask.parent.active=!0),this.weilanNode&&(6==this.vertical_count?(this.weilanNode.width=650,this.weilanNode.height=800):(this.weilanNode.width=750,this.weilanNode.height=1075))}else console.warn("catContainer is null, cannot display animals")},e.prototype.getCatNode=function(t){var e;if(null==t.cat){var o=p.default.get("AnimalItem");if(!o)return console.error("AnimalItem\u9884\u5236\u4f53\u672a\u627e\u5230"),null;e=cc.instantiate(o)}else e=t.cat;var n=Math.floor(t.now_grids[0]/1e4),i=t.now_grids[0]%1e4;return n=n*this.ww-this.ww/2*this.vertical_count+this.ww/2,i=this.ww/2*this.horizontal_count-i*this.ww-this.ww/2,t.dir==l.default.up?(i+=this.ww/2,e.angle=0):t.dir==l.default.down?(i-=this.ww/2,e.angle=180):t.dir==l.default.left?(n-=this.ww/2,e.angle=90):t.dir==l.default.right&&(n+=this.ww/2,e.angle=270),e.x=n,e.y=i,e},e.prototype.onClickCatNode=function(t){if(I.default.playSound(b.default.SHEEP),0!=this.is_game_playing){this.maskNode.active=!1;for(var e=0;e<this.cat_info_list.length;e++)this.cat_info_list[e].cat.zIndex=0;for(e=0;e<this.cat_info_list.length;e++)if(this.cat_info_list[e].cat==t.target)return void(1==this.fz_flag?(console.log("AAA"),this.resetDirection2(this.cat_info_list[e])):1==this.xiaochu_flag?(console.log("AAA2"),this.xiaochuayang(this.cat_info_list[e])):this.onMoveCat(this.cat_info_list[e]))}},e.prototype.xiaochuayang=function(t){0!=this.cat_info_list.length&&(this.buttonsNode&&(this.buttonsNode.active=!0),this.xiaochu_flag=!1,this.selectNode&&(this.selectNode.active=!1),this.xiaochuayangcao(t))},e.prototype.xiaochuayangcao=function(t){var e=this;t.dir==l.default.up?(t.move_tween=cc.tween(t.cat).parallel(cc.tween().by(.1,{y:15}).by(2,{y:2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t)):t.dir==l.default.down?(t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{y:t.cat.y-15}).to(2,{y:t.cat.y-2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t)):t.dir==l.default.left?(t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{x:t.cat.x-15}).to(2,{x:t.cat.x-2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t)):t.dir==l.default.right&&(t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{x:t.cat.x+15}).to(2,{x:t.cat.x+2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t))},e.prototype.onMoveCat=function(t){for(var e=this,o=0;o<t.now_grids.length;o++)this.grid_map.set(t.now_grids[o],0);var n=Math.floor(t.now_grids[0]/1e4),i=t.now_grids[0]%1e4,a="";switch(t.dir){case l.default.up:a="\u5411\u4e0a";break;case l.default.down:a="\u5411\u4e0b";break;case l.default.left:a="\u5411\u5de6";break;case l.default.right:a="\u5411\u53f3"}cc.log("\u52a8\u7269\u79fb\u52a8\uff1a\u65b9\u5411="+a+"("+t.dir+"), \u89d2\u5ea6="+t.cat.angle+", \u7f51\u683c\u4f4d\u7f6e=("+n+","+i+")"),t.move_tween&&t.lastPosition&&(t.move_tween.stop(),t.cat.x=t.lastPosition.x,t.cat.y=t.lastPosition.y),t.lastPosition=new cc.Vec2(t.cat.x,t.cat.y),t.cat.x,t.cat.y;var r=0;if(t.dir==l.default.up){for(i-=1,cc.log("\u52a8\u7269\u5411\u4e0a\u79fb\u52a8\uff1a\u8d77\u59cb\u4f4d\u7f6e i="+n+", k="+i+", \u7f51\u683c\u8fb9\u754c horizontal_count="+this.horizontal_count);;){var s=1e4*n+(f=i-(r+=1));if(f<0){cc.log("\u52a8\u7269\u5411\u4e0a\u79fb\u52a8\uff1a\u8d85\u51fa\u4e0a\u8fb9\u754c\uff0cnextK="+f+" < 0\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(!this.grid_map.has(s)){cc.log("\u52a8\u7269\u5411\u4e0a\u79fb\u52a8\uff1a\u7f51\u683c\u4e0d\u5b58\u5728\uff0cgridId="+s+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(1==this.grid_map.get(s)){r-=1,cc.log("\u52a8\u7269\u5411\u4e0a\u79fb\u52a8\uff1a\u78b0\u649e\u5230\u969c\u788d\u7269\uff0cgridId="+s+"\uff0c\u79fb\u52a8\u8ddd\u79bb="+r);break}}if(0!=r&&(t.push_last(),this.old_cat_pos.push(t)),100==r)t.move_tween=cc.tween(t.cat).parallel(cc.tween().by(.1,{y:15}).by(2,{y:2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t);else{t.move_tween=cc.tween(t.cat).to(.1,{y:t.cat.y+15}).to(.1*r,{y:t.cat.y+15+this.ww*r}).delay(.1).to(.1,{y:t.cat.y+15+this.ww*r-15}).call(function(){e.onAnimalCollison(t),t.move_tween=null,t.lastPosition=null}).start();for(var c=0;c<t.now_grids.length;c++){var u=Math.floor(t.now_grids[c]/1e4),p=t.now_grids[c]%1e4;p-=r,t.now_grids[c]=1e4*u+p,this.grid_map.set(t.now_grids[c],1)}}}else if(t.dir==l.default.down){for(i+=1,cc.log("\u52a8\u7269\u5411\u4e0b\u79fb\u52a8\uff1a\u8d77\u59cb\u4f4d\u7f6e i="+n+", k="+i+", \u7f51\u683c\u8fb9\u754c horizontal_count="+this.horizontal_count);;){var f;if(s=1e4*n+(f=i+(r+=1)),f>=this.horizontal_count){cc.log("\u52a8\u7269\u5411\u4e0b\u79fb\u52a8\uff1a\u8d85\u51fa\u4e0b\u8fb9\u754c\uff0cnextK="+f+" >= "+this.horizontal_count+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(!this.grid_map.has(s)){cc.log("\u52a8\u7269\u5411\u4e0b\u79fb\u52a8\uff1a\u7f51\u683c\u4e0d\u5b58\u5728\uff0cgridId="+s+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(1==this.grid_map.get(s)){r-=1,cc.log("\u52a8\u7269\u5411\u4e0b\u79fb\u52a8\uff1a\u78b0\u649e\u5230\u969c\u788d\u7269\uff0cgridId="+s+"\uff0c\u79fb\u52a8\u8ddd\u79bb="+r);break}}if(0!=r&&(t.push_last(),this.old_cat_pos.push(t)),100==r)t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{y:t.cat.y-15}).to(2,{y:t.cat.y-2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t);else{t.move_tween=cc.tween(t.cat).to(.1,{y:t.cat.y-15}).to(.1*r,{y:t.cat.y-15-this.ww*r}).delay(.1).to(.1,{y:t.cat.y-15-this.ww*r+15}).call(function(){e.onAnimalCollison(t),t.move_tween=null,t.lastPosition=null}).start();for(var h=0;h<t.now_grids.length;h++)u=Math.floor(t.now_grids[h]/1e4),p=t.now_grids[h]%1e4,p+=r,t.now_grids[h]=1e4*u+p,this.grid_map.set(t.now_grids[h],1)}}else if(t.dir==l.default.left){for(n-=1,cc.log("\u52a8\u7269\u5411\u5de6\u79fb\u52a8\uff1a\u8d77\u59cb\u4f4d\u7f6e i="+n+", k="+i+", \u7f51\u683c\u8fb9\u754c vertical_count="+this.vertical_count);;){if(s=1e4*(g=n-(r+=1))+i,g<0){cc.log("\u52a8\u7269\u5411\u5de6\u79fb\u52a8\uff1a\u8d85\u51fa\u5de6\u8fb9\u754c\uff0cnextI="+g+" < 0\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(!this.grid_map.has(s)){cc.log("\u52a8\u7269\u5411\u5de6\u79fb\u52a8\uff1a\u7f51\u683c\u4e0d\u5b58\u5728\uff0cgridId="+s+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(1==this.grid_map.get(s)){r-=1,cc.log("\u52a8\u7269\u5411\u5de6\u79fb\u52a8\uff1a\u78b0\u649e\u5230\u969c\u788d\u7269\uff0cgridId="+s+"\uff0c\u79fb\u52a8\u8ddd\u79bb="+r);break}}if(0!=r&&(t.push_last(),this.old_cat_pos.push(t)),100==r)t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{x:t.cat.x-15}).to(2,{x:t.cat.x-2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t);else{t.move_tween=cc.tween(t.cat).to(.1,{x:t.cat.x-15}).to(.1*r,{x:t.cat.x-15-this.ww*r}).delay(.1).to(.1,{x:t.cat.x-15-this.ww*r+15}).call(function(){e.onAnimalCollison(t),t.move_tween=null}).start();for(var d=0;d<t.now_grids.length;d++)u=Math.floor(t.now_grids[d]/1e4),p=t.now_grids[d]%1e4,u-=r,t.now_grids[d]=1e4*u+p,this.grid_map.set(t.now_grids[d],1)}}else if(t.dir==l.default.right){for(n+=1,cc.log("\u52a8\u7269\u5411\u53f3\u79fb\u52a8\uff1a\u8d77\u59cb\u4f4d\u7f6e i="+n+", k="+i+", \u7f51\u683c\u8fb9\u754c vertical_count="+this.vertical_count);;){var g;if(s=1e4*(g=n+(r+=1))+i,g>=this.vertical_count){cc.log("\u52a8\u7269\u5411\u53f3\u79fb\u52a8\uff1a\u8d85\u51fa\u53f3\u8fb9\u754c\uff0cnextI="+g+" >= "+this.vertical_count+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(!this.grid_map.has(s)){cc.log("\u52a8\u7269\u5411\u53f3\u79fb\u52a8\uff1a\u7f51\u683c\u4e0d\u5b58\u5728\uff0cgridId="+s+"\uff0c\u53ef\u4ee5\u79bb\u5f00"),r=100;break}if(1==this.grid_map.get(s)){r-=1,cc.log("\u52a8\u7269\u5411\u53f3\u79fb\u52a8\uff1a\u78b0\u649e\u5230\u969c\u788d\u7269\uff0cgridId="+s+"\uff0c\u79fb\u52a8\u8ddd\u79bb="+r);break}}if(0!=r&&(t.push_last(),this.old_cat_pos.push(t)),100==r)t.move_tween=cc.tween(t.cat).parallel(cc.tween().to(.1,{x:t.cat.x+15}).to(2,{x:t.cat.x+2e3}),cc.tween().call(function(){e.createTrailEffect(t)}),cc.tween().delay(1).call(function(){e.showTaskProgressAfterMovement(t)})).call(function(){t.move_tween=null,t.lastPosition=null}).start(),this.removeCat(t);else{t.move_tween=cc.tween(t.cat).to(.1,{x:t.cat.x+15}).to(.1*r,{x:t.cat.x+15+this.ww*r}).delay(.1).to(.1,{x:t.cat.x+15+this.ww*r-15}).call(function(){e.onAnimalCollison(t),t.move_tween=null,t.lastPosition=null}).start();for(var m=0;m<t.now_grids.length;m++)u=Math.floor(t.now_grids[m]/1e4),p=t.now_grids[m]%1e4,u+=r,t.now_grids[m]=1e4*u+p,this.grid_map.set(t.now_grids[m],1)}}this.deleteStepCount()},e.prototype.createTrailEffect=function(t){if(t.cat){var e=t.cat.getComponent("AnimalItem");e&&e.createTrailEffect()}},e.prototype.showTaskProgressAfterMovement=function(t){var e=this;if(0!=this.isRewardSkinLevel){var o=new cc.Vec3,n=new cc.Vec3;if(t.cat.getPosition(o),t.cat.parent.convertToWorldSpaceAR(o,n),null!=this.taskProgressMask){this.taskProgressMask.getPosition(o);var i,a=new cc.Vec3;if(this.taskProgressMask.parent.convertToWorldSpaceAR(o,a),this.rewardItemPool.size()>0)i=this.rewardItemPool.get();else{var r=p.default.get("RewardItem");if(!r)return void console.error("RewardItem\u9884\u5236\u4f53\u672a\u627e\u5230");i=cc.instantiate(r)}this.catContainer&&(this.catContainer.addChild(i),i.setPosition(this.catContainer.convertToNodeSpaceAR(n))),cc.tween(i).to(.6,{position:i.parent.convertToNodeSpaceAR(a)}).call(function(){i.removeFromParent(),e.rewardItemPool.put(i),e.updateTaskProgressAfterReward(e.cat_info_list.length)}).start()}}},e.prototype.updateTaskProgressAfterReward=function(t){var e,o=this;if(e=(this.taskProgress-t)/this.taskProgress,this.taskProgressLabel&&(this.taskProgressLabel.string="\u8fdb\u5ea6 "+(100*e).toFixed(2)+"%"),this.taskProgressMask){var n=this.taskProgressMask.children[0];if(n){var i=n.height*n.scaleY;cc.tween(this.taskProgressMask).to(1,{height:i*e}).call(function(){1==e&&(o.isTaskProgressTweening=!1)}).start()}}},e.prototype.removeCat=function(t){1==this.isRewardSkinLevel&&(this.isTaskProgressTweening=!0);for(var e=0;e<t.now_grids.length;e++)this.grid_map.set(t.now_grids[e],0);for(var o=0;o<this.cat_info_list.length;o++)if(this.cat_info_list[o]==t){this.combo(),this.cat_info_list.splice(o,1);break}console.log("\u81ea\u52a8\u73a9\uff1a\u52a8\u7269\u5df2\u79fb\u9664\uff0c\u6e05\u7406\u4e86"+t.now_grids.length+"\u4e2a\u7f51\u683c\u4f4d\u7f6e")},e.prototype.combo=function(){if(this.comboContainer){var t=this.comboContainer.getComponent("ComboUI");t&&t.updateProgressBar()}},e.prototype.resetComboUi=function(){if(this.comboContainer){var t=this.comboContainer.getComponent("ComboUI");t&&t.resetUi()}},e.prototype.checkShow=function(){var t=[],e=this.node.convertToWorldSpaceAR(new cc.Vec2(0,0)),o=this.showNode.height,n=this.showNode.width;cc.sys.getSafeAreaRect()&&(o=.7*cc.sys.getSafeAreaRect().height,n=.9*cc.sys.getSafeAreaRect().width);for(var i=e.y-o/2-50,a=e.y+o/2-50,r=e.x-n/2,s=e.x+n/2,c=0;c<this.cat_info_list.length;c++){var l=(h=this.cat_info_list[c]).cat.parent.convertToWorldSpaceAR(h.cat.position);l.x<r?t.push(h):l.x>s?t.push(h):l.y<i?t.push(h):l.y>a&&t.push(h)}for(c=0;c<t.length;c++)for(var u=0;u<this.cat_info_list.length;u++)if(this.cat_info_list[u]==t[c]){this.cat_info_list[u].cat.parent=null;for(var p=0;p<this.cat_info_list[u].now_grids.length;p++)this.grid_map.set(this.cat_info_list[u].now_grids[p],0);this.cat_info_list.splice(u,1);break}for(this.checkDirection(),c=0;c<this.cat_info_list.length;c++){var h;(h=this.cat_info_list[c]).cat.scale=0,h.cat.opacity=255,cc.tween(h.cat).delay(f.default.randomRange(100,1500)/1e3).to(.5,{scale:1}).start()}this.is_game_playing=!0,this.stepCount=this.cat_info_list.length+10,this.updateStepStr()},e.prototype.chatCatIsOut=function(){for(var t=[],e=0;e<this.cat_info_list.length;e++){var o=this.cat_info_list[e],n=Math.floor(o.now_grids[0]/1e4),i=o.now_grids[0]%1e4;if(0!=this.grid_map.get(o.now_grids[0])){var a=0;if(o.dir==l.default.up){for(i-=1;;){if(i-(a+=1)<0){a=100;break}if(1==this.grid_map.get(1e4*n+(i-a))){a-=1;break}}100==a&&t.push(o)}else if(o.dir==l.default.down){for(i+=1;;){if(i+(a+=1)>=this.horizontal_count){a=100;break}if(1==this.grid_map.get(1e4*n+(i+a))){a-=1;break}}100==a&&t.push(o)}else if(o.dir==l.default.left){for(n-=1;;){if(n-(a+=1)<0){a=100;break}if(1==this.grid_map.get(1e4*(n-a)+i)){a-=1;break}}100==a&&t.push(o)}else if(o.dir==l.default.right){for(n+=1;;){if(n+(a+=1)>=this.vertical_count){a=100;break}if(1==this.grid_map.get(1e4*(n+a)+i)){a-=1;break}}100==a&&t.push(o)}}}return t},e.prototype.canAllCatOut=function(){for(var t=[];;){var e=this.chatCatIsOut();if(0==e.length)break;for(var o=0;o<e.length;o++)for(var n=0;n<e[o].now_grids.length;n++)t.push(e[o].now_grids[n]),this.grid_map.set(e[o].now_grids[n],0);if(t.length==2*this.cat_info_list.length){for(o=0;o<t.length;o++)this.grid_map.set(t[o],1);return!0}}return!1},e.prototype.onClickRevoke=function(){if(0!=this.old_cat_pos.length){var t=this.old_cat_pos.pop();null!=t.move_tween&&t.move_tween.stop();for(var e=0;e<t.now_grids.length;e++)this.grid_map.set(t.now_grids[e],0);for(t.revoke(),e=0;e<t.now_grids.length;e++)this.grid_map.set(t.now_grids[e],1);t.cat=this.getCatNode(t),-1==this.cat_info_list.indexOf(t)&&this.cat_info_list.push(t)}else d.default.inst.showTips("\u6ca1\u6709\u53ef\u64a4\u56de\u7684\u64cd\u4f5c")},e.prototype.onClickLightCat=function(){var t=this.chatCatIsOut();if(0!=t.length){this.maskNode.active=!0,this.maskNode.zIndex=99;for(var e=0;e<t.length;e++)t[e].cat.zIndex=100}else d.default.inst.showTips("\u6ca1\u6709\u53ef\u6d88\u9664\u7684\u52a8\u7269")},e.prototype.onClickResetDirection=function(){if(0!=this.cat_info_list.length){this.buttonsNode&&(this.buttonsNode.active=!1),this.fz_flag=!0,this.selectNode&&(this.selectNode.active=!0);for(var t=0;t<this.cat_info_list.length;t++)this.cat_info_list[t].cat.zIndex=100}},e.prototype.onClickAddTime=function(){if(console.log("\u8fd9\u91cc\u662f\u6d88\u9664\u64cd\u4f5c"),0!=this.cat_info_list.length){this.buttonsNode&&(this.buttonsNode.active=!1),this.xiaochu_flag=!0,this.selectNode&&(this.selectNode.active=!0);for(var t=0;t<this.cat_info_list.length;t++)this.cat_info_list[t].cat.zIndex=100}},e.prototype.resetDirection2=function(t){0!=this.cat_info_list.length&&(this.buttonsNode&&(this.buttonsNode.active=!0),this.fz_flag=!1,this.selectNode&&(this.selectNode.active=!1),this.resetDirection3(t))},e.prototype.resetDirection3=function(t){if(t.dir=(t.dir-1+2)%4+1,t.now_grids.reverse(),t.cat){var e=this.getCatPositionAndAngle(t);t.cat.x=e.pos.x,t.cat.y=e.pos.y,t.cat.angle=e.angle}else t.cat=this.getCatNode(t)},e.prototype.onTimerUpdate=function(){0!=this.is_game_playing&&0!=this.cat_info_list.length&&1!=this.is_game_pause&&this.updateTimeLabel()},e.prototype.updateTimeLabel=function(){},e.prototype.checkDirection=function(){for(var t=this,e=new Map,o=new Map,n=0;n<this.cat_info_list.length;n++){var i=this.cat_info_list[n];if(i.dir==l.default.left||i.dir==l.default.right){var a=Math.floor(i.now_grids[0]%1e4);i.dir==l.default.right&&this.resetDirection3(i),0==e.has(a)&&e.set(a,new Array),e.get(a).push(i)}else i.dir==l.default.down&&this.resetDirection3(i),a=Math.floor(i.now_grids[0]/1e4),0==o.has(a)&&o.set(a,new Array),o.get(a).push(i)}e.forEach(function(e){e.sort(function(t,e){return Math.floor(t.now_grids[0]/1e4)-Math.floor(e.now_grids[0]/1e4)});for(var o=f.default.randomRange(0,e.length+1);o<e.length;o++)t.resetDirection3(e[o])}),o.forEach(function(e){e.sort(function(t,e){return Math.floor(t.now_grids[0]%1e4)-Math.floor(e.now_grids[0]%1e4)});for(var o=f.default.randomRange(0,e.length+1);o<e.length;o++)t.resetDirection3(e[o])})},e.prototype.update=function(){1==this.is_game_playing&&0==this.isTaskProgressTweening&&0==this.cat_info_list.length&&(this.is_game_playing=!1,this.onGameOver())},e.prototype.onGameOver=function(){var t=this,e=this.isAutoPlaying||o.globalAutoPlayState;console.log("=== \u6e38\u620f\u7ed3\u675f ==="),console.log("\u5f53\u524d\u5b9e\u4f8b\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),console.log("\u5168\u5c40\u81ea\u52a8\u73a9\u72b6\u6001:",o.globalAutoPlayState),console.log("\u6700\u7ec8\u4f20\u9012\u7ed9WinUI\u7684\u72b6\u6001:",e),this.stopAutoPlay(),g.default.lv++,setTimeout(function(){I.default.playSound(b.default.LEVELUP),t.resetComboUi(),console.log("\u8c03\u7528WinUI.showUI\uff0c\u4f20\u9012\u53c2\u6570:",e),m.default.inst.showUI(e)},1e3)},e.prototype.onClickPause=function(){this.is_game_pause=!0,I.default.playSound(b.default.CLICK),y.default.inst.showUI()},e.prototype.useBuffItem=function(t){switch(t.identifier){case P.BuffItems.AddTime:this.onClickAddTime(),g.default.consumeBuffItem(P.BuffItems.AddTime);break;case P.BuffItems.Inverse:this.onClickResetDirection(),g.default.consumeBuffItem(P.BuffItems.Inverse);break;case P.BuffItems.Hint:this.onClickLightCat(),g.default.consumeBuffItem(P.BuffItems.Hint);break;case P.BuffItems.Undo:this.onClickRevoke(),g.default.consumeBuffItem(P.BuffItems.Undo);break;case P.BuffItems.SwapAnimal:this.swapAnimalBtn(),g.default.consumeBuffItem(P.BuffItems.SwapAnimal)}t.init(),this.is_game_pause=!1},e.prototype.swapAnimalBtn=function(){if(cc.log("\u5237\u65b0\u5e03\u5c40\uff1a\u5feb\u901f\u91cd\u65b0\u6392\u5217\u73b0\u6709\u52a8\u7269\u4f4d\u7f6e"),0!==this.cat_info_list.length){var t=this.cat_info_list.length;cc.log("\u5237\u65b0\u5e03\u5c40\uff1a\u4fdd\u6301"+t+"\u4e2a\u52a8\u7269\u4e0d\u53d8");for(var e=this.cat_info_list.map(function(t){return t.cat}),o=0;o<this.cat_info_list.length;o++)for(var n=0;n<this.cat_info_list[o].now_grids.length;n++)this.grid_map.set(this.cat_info_list[o].now_grids[n],0);for(this.cat_info_list=[],this.grid_num_list=[],o=0;o<this.vertical_count;o++)for(n=0;n<this.horizontal_count;n++)this.grid_num_list.push(1e4*o+n),this.grid_map.set(1e4*o+n,0);f.default.randomRangeSort(this.grid_num_list);for(var i=0,a=0;a<t&&i<1e4&&!(this.grid_num_list.length<2);){f.default.randomRangeSort(this.dirs);var r=!1;for(o=0;o<4;o++){var s=this.grid_num_list[0],c=this.getNextGrid(s,this.dirs[o]);if(-1!=c&&1!=this.grid_map.get(c)){(l=new u.default).dir=this.dirs[o],l.now_grids.push(s),l.now_grids.push(c),l.cat=e[a],this.grid_map.set(s,1),this.grid_map.set(c,1),f.default.deleteValue(this.grid_num_list,s),f.default.deleteValue(this.grid_num_list,c),this.cat_info_list.push(l),a++,r=!0;break}}r||this.grid_num_list.shift(),i++}for(o=0;o<this.cat_info_list.length;o++){var l=this.cat_info_list[o],p=this.getCatPositionAndAngle(l);l.cat.x=p.pos.x,l.cat.y=p.pos.y,l.cat.angle=p.angle,l.cat.scale=0,l.cat.opacity=255,cc.tween(l.cat).delay(.02*o).to(.2,{scale:1}).start()}cc.log("\u5237\u65b0\u5e03\u5c40\u5b8c\u6210\uff1a\u5feb\u901f\u91cd\u65b0\u6392\u5217\u4e86"+this.cat_info_list.length+"\u4e2a\u52a8\u7269")}else cc.warn("\u6ca1\u6709\u52a8\u7269\u53ef\u4ee5\u91cd\u65b0\u6392\u5217")},e.prototype.getCatPositionAndAngle=function(t){var e=Math.floor(t.now_grids[0]/1e4),o=t.now_grids[0]%1e4;e=e*this.ww-this.ww/2*this.vertical_count+this.ww/2,o=this.ww/2*this.horizontal_count-o*this.ww-this.ww/2;var n=0;return t.dir==l.default.up?(o+=this.ww/2,n=0):t.dir==l.default.down?(o-=this.ww/2,n=180):t.dir==l.default.left?(e-=this.ww/2,n=90):t.dir==l.default.right&&(e+=this.ww/2,n=270),{pos:new cc.Vec2(e,o),angle:n}},e.prototype.onAnimalCollison=function(t){var e=t.cat.getComponent("AnimalItem");e&&e.playCollisionAnimation()},e.prototype.startAutoPlay=function(){this.is_game_playing&&(this.isAutoPlaying=!0,o.globalAutoPlayState=!0,console.log("\u5f00\u59cb\u81ea\u52a8\u73a9\uff0c\u5168\u5c40\u72b6\u6001\u5df2\u66f4\u65b0:",o.globalAutoPlayState),this.updateAutoPlayButtonText(),this.executeAutoPlay())},e.prototype.stopAutoPlay=function(){console.log("\u505c\u6b62\u81ea\u52a8\u73a9\uff0c\u5f53\u524d\u72b6\u6001:",this.isAutoPlaying),this.isAutoPlaying=!1,this.updateAutoPlayButtonText(),this.autoPlayTimer&&(clearTimeout(this.autoPlayTimer),this.autoPlayTimer=null)},e.prototype.stopAutoPlayCompletely=function(){console.log("\u5b8c\u5168\u505c\u6b62\u81ea\u52a8\u73a9"),this.isAutoPlaying=!1,o.globalAutoPlayState=!1,this.updateAutoPlayButtonText(),this.autoPlayTimer&&(clearTimeout(this.autoPlayTimer),this.autoPlayTimer=null)},e.prototype.updateAutoPlayButtonText=function(){if(this.autoPlayBtn){var t=this.autoPlayBtn.node.getChildByName("Label");if(t){var e=t.getComponent(cc.Label);e&&(e.string=this.isAutoPlaying?"\u505c\u6b62\u81ea\u52a8":"\u81ea\u52a8\u73a9")}}},e.prototype.executeAutoPlay=function(){var t=this;if(this.isAutoPlaying&&this.is_game_playing){if(0===this.cat_info_list.length)return console.log("\u81ea\u52a8\u73a9\uff1a\u6e38\u620f\u7ed3\u675f\uff0c\u505c\u6b62\u81ea\u52a8\u73a9"),void this.stopAutoPlay();console.log("\u81ea\u52a8\u73a9\uff1a\u5f00\u59cb\u65b0\u4e00\u8f6e\u68c0\u67e5\uff0c\u5f53\u524d\u52a8\u7269\u6570\u91cf\uff1a"+this.cat_info_list.length);var e=this.chatCatIsOut();if(console.log("\u81ea\u52a8\u73a9\uff1a\u68c0\u67e5\u53ef\u4ee5\u79bb\u5f00\u7684\u52a8\u7269\uff0c\u627e\u5230"+e.length+"\u4e2a"),e.length>0){console.log("\u81ea\u52a8\u73a9\uff1a\u627e\u5230"+e.length+"\u4e2a\u53ef\u4ee5\u79bb\u5f00\u7684\u52a8\u7269\uff0c\u6267\u884c\u79bb\u5f00");var o=e[0];return this.onMoveCat(o),void(this.autoPlayTimer=setTimeout(function(){t.executeAutoPlay()},1e3))}var n=this.findAnimalCanMove();if(n)return console.log("\u81ea\u52a8\u73a9\uff1a\u627e\u5230\u53ef\u4ee5\u79fb\u52a8\u4e00\u6bb5\u8ddd\u79bb\u7684\u52a8\u7269\uff0c\u6267\u884c\u79fb\u52a8"),this.onMoveCat(n),void(this.autoPlayTimer=setTimeout(function(){t.executeAutoPlay()},1e3));this.cat_info_list.length>0?(console.log("\u81ea\u52a8\u73a9\uff1a\u6ca1\u6709\u53ef\u79fb\u52a8\u7684\u52a8\u7269\uff0c\u4f7f\u7528\u6d88\u9664\u529f\u80fd"),this.autoUseEliminateFunction()):(console.log("\u81ea\u52a8\u73a9\uff1a\u6ca1\u6709\u52a8\u7269\u4e86\uff0c\u505c\u6b62\u81ea\u52a8\u73a9"),this.stopAutoPlay())}},e.prototype.autoUseEliminateFunction=function(){var t=this;console.log("\u81ea\u52a8\u73a9\uff1a\u5f00\u59cb\u6d88\u9664\u6d41\u7a0b - \u5148\u663e\u793a\u6d88\u9664UI"),this.onClickAddTime(),this.autoPlayTimer=setTimeout(function(){if(t.cat_info_list.length>0){console.log("\u81ea\u52a8\u73a9\uff1a\u81ea\u52a8\u9009\u62e9\u7b2c\u4e00\u53ea\u52a8\u7269\u8fdb\u884c\u6d88\u9664");var e=t.cat_info_list[0];t.xiaochuayang(e),t.autoPlayTimer=setTimeout(function(){console.log("\u81ea\u52a8\u73a9\uff1a\u6d88\u9664\u5b8c\u6210\uff0c\u91cd\u65b0\u5f00\u59cb\u68c0\u67e5"),t.executeAutoPlay()},1e3)}else console.log("\u81ea\u52a8\u73a9\uff1a\u6ca1\u6709\u52a8\u7269\u53ef\u6d88\u9664\uff0c\u505c\u6b62\u81ea\u52a8\u73a9"),t.stopAutoPlay()},1500)},e.prototype.findAnimalCanMove=function(){console.log("\u81ea\u52a8\u73a9\uff1a\u68c0\u67e5\u53ef\u4ee5\u79fb\u52a8\u4e00\u6bb5\u8ddd\u79bb\u7684\u52a8\u7269");for(var t=0;t<this.cat_info_list.length;t++){var e=this.cat_info_list[t];if(this.canAnimalMoveDistance(e))return console.log("\u81ea\u52a8\u73a9\uff1a\u627e\u5230\u53ef\u4ee5\u79fb\u52a8\u7684\u52a8\u7269\uff0c\u7d22\u5f15\uff1a"+t+"\uff0c\u65b9\u5411\uff1a"+e.dir),e}return console.log("\u81ea\u52a8\u73a9\uff1a\u6ca1\u6709\u627e\u5230\u53ef\u4ee5\u79fb\u52a8\u4e00\u6bb5\u8ddd\u79bb\u7684\u52a8\u7269"),null},e.prototype.canAnimalMoveDistance=function(t){var e=Math.floor(t.now_grids[0]/1e4),o=t.now_grids[0]%1e4;if(0==this.grid_map.get(t.now_grids[0]))return!1;var n=0;if(t.dir==l.default.up){for(o-=1;;){if(o-(n+=1)<0){n=100;break}if(1==this.grid_map.get(1e4*e+(o-n))){n-=1;break}}return n>0&&100!=n}if(t.dir==l.default.down){for(o+=1;;){if(o+(n+=1)>=this.horizontal_count){n=100;break}if(1==this.grid_map.get(1e4*e+(o+n))){n-=1;break}}return n>0&&100!=n}if(t.dir==l.default.left){for(e-=1;;){if(e-(n+=1)<0){n=100;break}if(1==this.grid_map.get(1e4*(e-n)+o)){n-=1;break}}return n>0&&100!=n}if(t.dir==l.default.right){for(e+=1;;){if(e+(n+=1)>=this.vertical_count){n=100;break}if(1==this.grid_map.get(1e4*(e+n)+o)){n-=1;break}}return n>0&&100!=n}return!1},e.prototype.checkVipStatus=function(){if(this.autoPlayBtn){var t=g.default.isVipUser;this.autoPlayBtn.node.active=t,t&&this.updateAutoPlayButtonText()}},e.prototype.onVipStatusChanged=function(){this.checkVipStatus()},e.prototype.onDestroy=function(){cc.systemEvent.off("vipStatusChanged",this.onVipStatusChanged,this)},e.globalAutoPlayState=!1,a([c([cc.SpriteFrame])],e.prototype,"gameBackgroundArray",void 0),a([c(cc.Node)],e.prototype,"catContainer",void 0),a([c(cc.Node)],e.prototype,"showNode",void 0),a([c(cc.Sprite)],e.prototype,"gameBg",void 0),a([c(cc.Node)],e.prototype,"maskNode",void 0),a([c(cc.Node)],e.prototype,"weilanNode",void 0),a([c(cc.Node)],e.prototype,"buttonsNode",void 0),a([c(cc.Node)],e.prototype,"selectNode",void 0),a([c(cc.Label)],e.prototype,"timerLabel",void 0),a([c(cc.Label)],e.prototype,"curLevel",void 0),a([c(cc.Label)],e.prototype,"stepLabel",void 0),a([c(cc.Node)],e.prototype,"taskProgressMask",void 0),a([c(cc.Label)],e.prototype,"taskProgressLabel",void 0),a([c(cc.Node)],e.prototype,"comboContainer",void 0),a([c(cc.Button)],e.prototype,"autoPlayBtn",void 0),a([c(A.default)],e.prototype,"animalColorBtn",void 0),o=a([s],e)}(h.default);o.default=U,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/CatInfo":"CatInfo","../datas/Constants":"Constants","../datas/DirectionType":"DirectionType","../datas/mySkinData":"mySkinData","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../scripts/item/AnimalColorBtn":"AnimalColorBtn","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","../utils/util/GameUtil":"GameUtil","./ChangeGameBgUI":"ChangeGameBgUI","./PauseUI":"PauseUI","./StepOverUI":"StepOverUI","./TimeOverUI":"TimeOverUI","./TipsUI":"TipsUI","./WinUI":"WinUI"}],GameUtil:[function(t,e,o){"use strict";cc._RF.push(e,"3cb5eRW+fBNvJQSAo5XEhJs","GameUtil"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){}return t.randomRange=function(t,e){return t+Math.floor(Math.random()*(e-t))},t.randomRangeSort=function(e){for(var o=0,n=e.length;o<n;o++){var i=t.randomRange(0,e.length),a=e[i];e[i]=e[o],e[o]=a}},t.deleteValue=function(t,e){for(var o=0;o<t.length;o++)if(t[o]==e)return void t.splice(o,1)},t}();o.default=n,cc._RF.pop()},{}],Grass:[function(t,e,o){"use strict";cc._RF.push(e,"9a800eC2sJCr5UIR7IEcikP","Grass");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=(r.property,t("../utils/util/GameUtil")),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.index=0,e.next=c.default.randomRange(60,500),e}return i(e,t),e.prototype.update=function(){this.index++,this.index>this.next&&(this.index=0,this.next=c.default.randomRange(600,1e3)),this.index>this.next-5?this.node.scale=1.2:this.node.scale=1},a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{"../utils/util/GameUtil":"GameUtil"}],HomeUI:[function(t,e,o){"use strict";cc._RF.push(e,"4cb83Bxzk1BN7SH9SyDkPgc","HomeUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../utils/manager/PrefabUtil"),u=t("../utils/UIbase"),p=t("./GameUI"),f=t("../utils/Palt"),h=t("./SettingUI"),d=t("./ShopUI"),g=t("./rankUI"),m=t("./regionalUI"),y=t("../manager/AudioMgr"),_=t("../datas/AudioPath"),v=t("./ChooseAreaUI"),b=t("./SkinGalleryUI"),I=t("../manager/LocalData"),C=t("../datas/provincesData"),P=t("./TipsUI"),w=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.provinceContainer=null,e.yardPre=null,e.scrollView=null,e.userSkinInYard=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=l.default.get("HomeUI");if(!t)return console.error("HomeUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);if(!e)return console.error("\u9884\u5236\u4f53\u5b9e\u4f8b\u5316\u5931\u8d25"),null;this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.onLoad=function(){var t=this;this.scrollToPoint(),cc.systemEvent.on("selectSkin",function(e){e.id&&t.userSkinInYard&&t.userSkinInYard.changeSkin()}),cc.systemEvent.on("provinceSelect",function(){t.showProvinceRank(),t.scrollToPoint()}),C.default.generateRegionTeamScores()},e.prototype.onShow=function(){f.default.showInterstitialAd(),f.default.showMoban(),b.default.inst.initializeData(),this.showProvinceRank(),y.default.playBgm()},e.prototype.scrollToPoint=function(){var t=this;""==I.default.userRegional?this.scheduleOnce(function(){t.scrollView.scrollToPercentVertical(1)},.1):this.scheduleOnce(this.onclickToMyPosition,.1)},e.prototype.notifyComingSoon=function(){P.default.inst.showTips("\u5efa\u8bbe\u4e2d\u2026\u2026")},e.prototype.showProvinceRank=function(){var t=C.default.getProvinceRank();if(this.yardPre){this.provinceContainer.removeAllChildren();for(var e=0;e<t.length;e++){var o=cc.instantiate(this.yardPre),n=o.getComponent("AnimalYard");if(!n)return;n.setRank(e+1),n.setProvinceName(t[e].name),n.setScore(t[e].score),this.provinceContainer.addChild(o),I.default.userRegional==t[e].id&&(this.userSkinInYard=n,n.pickUserAnimal(),n.changeSkin(),n.switchActiveOfPoint())}}},e.prototype.onclickToMyPosition=function(){var t=this;this.scrollView&&(this.scrollView.stopAutoScroll(),this.scheduleOnce(function(){t.scrollView.scrollToPercentVertical(C.default.findUserProvincePercentage())},.1))},e.prototype.onHide=function(){f.default.hideBanner()},e.prototype.onClickStartGame=function(){this.hideUI(),p.default.inst.onStartGame(),p.default.inst.animalColorBtn&&p.default.inst.animalColorBtn.resetColorMode(),y.default.playSound(_.default.CLICK)},e.prototype.onClickShare=function(){f.default.shareAppMessage(),y.default.playSound(_.default.CLICK)},e.prototype.onClickRank=function(){g.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onClickProvinceRank=function(){m.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onClickSettingBtn=function(){h.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onClickChooseAreaBtn=function(){v.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onClickShop=function(){d.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onClickSkinGallery=function(){b.default.inst.showUI(),y.default.playSound(_.default.CLICK)},e.prototype.onJump=function(t,e){null!=f.default.pt.navigateToMiniProgram&&f.default.pt.navigateToMiniProgram({appId:e})},a([c(cc.Node)],e.prototype,"provinceContainer",void 0),a([c(cc.Prefab)],e.prototype,"yardPre",void 0),a([c(cc.ScrollView)],e.prototype,"scrollView",void 0),o=a([s],e)}(u.default);o.default=w,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/provincesData":"provincesData","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./ChooseAreaUI":"ChooseAreaUI","./GameUI":"GameUI","./SettingUI":"SettingUI","./ShopUI":"ShopUI","./SkinGalleryUI":"SkinGalleryUI","./TipsUI":"TipsUI","./rankUI":"rankUI","./regionalUI":"regionalUI"}],I18nLable:[function(t,e,o){"use strict";cc._RF.push(e,"67a8dO5KeFIi6n06rX4BDSt","I18nLable");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("./I18n"),s=cc._decorator.executeInEditMode,c=cc._decorator,l=c.ccclass,u=c.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.label=null,e.key="",e}return i(e,t),Object.defineProperty(e.prototype,"_key",{get:function(){return this.key},set:function(t){this.key=t,this.updateLabel()},enumerable:!1,configurable:!0}),e.prototype.onLoad=function(){this.fetchRender()},e.prototype.onEnable=function(){this.updateLabel()},e.prototype.fetchRender=function(){var t=this.getComponent("cc.Label");if(t)return this.label=t,void this.updateLabel()},e.prototype.updateLabel=function(){this.label&&(this.label.string=r.default.t(this.key))},a([u({visible:!1})],e.prototype,"key",void 0),a([u({displayName:"\u7ffb\u8bd1id",visible:!0})],e.prototype,"_key",null),a([l,s()],e)}(cc.Component);o.default=p,cc._RF.pop()},{"./I18n":"I18n"}],I18n:[function(t,e,o){"use strict";cc._RF.push(e,"3e8fdZcDsxDW6mTeI5xi+bP","I18n");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=t("./language/Zh"),a=t("./language/En"),r=cc._decorator,s=r.ccclass,c=(r.property,function(){function t(){}return t.init=function(t){this.ready=!0,this.language=t,null==this.obj&&(this.obj={},this.obj.zh=i.default.zh,this.obj.en=a.default.en)},t.t=function(t){0==this.ready&&this.init(this.language);var e=this.obj[this.language][t];return null==e?"":e},t.updateSceneRenderers=function(){for(var t=cc.director.getScene().children,e=[],o=0;o<t.length;++o){var n=t[o].getComponentsInChildren("I18nLable");e=e.concat(n)}for(o=0;o<e.length;++o){var i=e[o];i.node.active&&i.updateLabel()}},t.language="en",t.ready=!1,n([s],t)}());o.default=c,cc._RF.pop()},{"./language/En":"En","./language/Zh":"Zh"}],InvitationCodes:[function(t,e,o){"use strict";cc._RF.push(e,"89dfc+YVv1HdZHLJ+k6Rqlf","InvitationCodes");var n=this&&this.__spreadArrays||function(){for(var t=0,e=0,o=arguments.length;e<o;e++)t+=arguments[e].length;var n=Array(t),i=0;for(e=0;e<o;e++)for(var a=arguments[e],r=0,s=a.length;r<s;r++,i++)n[i]=a[r];return n};Object.defineProperty(o,"__esModule",{value:!0});var i=function(){function t(){}return t.validateCode=function(t){if(!t||6!==t.length)return!1;var e=t.toUpperCase().trim(),o=this.CODES.includes(e);return o&&console.log("\u2705 \u5151\u6362\u7801 "+e+" \u9a8c\u8bc1\u6210\u529f - \u652f\u6301\u65e0\u9650\u6b21\u4f7f\u7528"),o},t.getAllCodes=function(){return n(this.CODES)},t.getCodeCount=function(){return this.CODES.length},t.getRandomCode=function(){var t=Math.floor(Math.random()*this.CODES.length);return this.CODES[t]},t.CODES=["A7K9M2","B8L3N4","C9M4O5","D1N5P6","E2O6Q7","F3P7R8","G4Q8S9","H5R9T1","I6S1U2","J7T2V3","K8U3W4","L9V4X5","M1W5Y6","N2X6Z7","O3Y7A8","P4Z8B9","Q5A9C1","R6B1D2","S7C2E3","T8D3F4","U9E4G5","V1F5H6","W2G6I7","X3H7J8","Y4I8K9","Z5J9L1","A6K1M2","B7L2N3","C8M3O4","D9N4P5","E1O5Q6","F2P6R7","G3Q7S8","H4R8T9","I5S9U1","J6T1V2","K7U2W3","L8V3X4","M9W4Y5","N1X5Z6","O2Y6A7","P3Z7B8","Q4A8C9","R5B9D1","S6C1E2","T7D2F3","U8E3G4","V9F4H5","W1G5I6","X2H6J7","Y3I7K8","Z4J8L9","A5K9M1","B6L1N2","C7M2O3","D8N3P4","E9O4Q5","F1P5R6","G2Q6S7","H3R7T8","I4S8U9","J5T9V1","K6U1W2","L7V2X3","M8W3Y4","N9X4Z5","O1Y5A6","P2Z6B7","Q3A7C8","R4B8D9","S5C9E1","T6D1F2","U7E2G3","V8F3H4","W9G4I5","X1H5J6","Y2I6K7","Z3J7L8","A4K8M9","B5L9N1","C6M1O2","D7N2P3","E8O3Q4","F9P4R5","G1Q5S6","H2R6T7","I3S7U8","J4T8V9","K5U9W1","L6V1X2","M7W2Y3","N8X3Z4","O9Y4A5","P1Z5B6","Q2A6C7","R3B7D8","S4C8E9","T5D9F1","U6E1G2","V7F2H3","W8G3I4","X9H4J5","Y1I5K6","Z2J6L7","A3K7M8","B4L8N9","C5M9O1","D6N1P2","E7O2Q3","F8P3R4","G9Q4S5","H1R5T6","I2S6U7","J3T7V8","K4U8W9","L5V9X1","M6W1Y2","N7X2Z3","O8Y3A4","P9Z4B5","Q1A5C6","R2B6D7","S3C7E8","T4D8F9","U5E9G1","V6F1H2","W7G2I3","X8H3J4","Y9I4K5","Z1J5L6","A2K6M7","B3L7N8","C4M8O9","D5N9P1","E6O1Q2","F7P2R3","G8Q3S4","H9R4T5","I1S5U6","J2T6V7","K3U7W8","L4V8X9","M5W9Y1","N6X1Z2","O7Y2A3","P8Z3B4","Q9A4C5","R1B5D6","S2C6E7","T3D7F8","U4E8G9","V5F9H1","W6G1I2","X7H2J3","Y8I3K4","Z9J4L5","A1K5M6","B2L6N7","C3M7O8","D4N8P9","E5O9Q1","F6P1R2","G7Q2S3","H8R3T4","I9S4U5","J1T5V6","K2U6W7","L3V7X8","M4W8Y9","N5X9Z1","O6Y1A2","P7Z2B3","Q8A3C4","R9B4D5","S1C5E6","T2D6F7","U3E7G8","V4F8H9","W5G9I1","X6H1J2","Y7I2K3","Z8J3L4","A9K4M5","B1L5N6","C2M6O7","D3N7P8","E4O8Q9","F5P9R1","G6Q1S2","H7R2T3","I8S3U4","J9T4V5","K1U5W6","L2V6X7","M3W7Y8","N4X8Z9","O5Y9A1","P6Z1B2","Q7A2C3","R8B3D4","S9C4E5","T1D5F6","U2E6G7","V3F7H8","W4G8I9","X5H9J1","Y6I1K2","Z7J2L3","A8K3M4","B9L4N5","C1M5O6","D2N6P7","E3O7Q8","F4P8R9","G5Q9S1","H6R1T2","I7S2U3","J8T3V4","K9U4W5","L1V5X6","M2W6Y7","N3X7Z8","O4Y8A9","P5Z9B1","Q6A1C2","R7B2D3","S8C3E4","T9D4F5","U1E5G6","V2F6H7","W3G7I8","X4H8J9","Y5I9K1","Z6J1L2","A7K2M3","B8L3N4","C9M4O5","D1N5P6","E2O6Q7","F3P7R8","G4Q8S9","H5R9T1","I6S1U2","J7T2V3","K8U3W4","L9V4X5","M1W5Y6","N2X6Z7","O3Y7A8","P4Z8B9","Q5A9C1","R6B1D2","S7C2E3","T8D3F4","U9E4G5","V1F5H6","W2G6I7","X3H7J8","Y4I8K9","Z5J9L1","A6K1M2","B7L2N3","C8M3O4","D9N4P5","E1O5Q6","F2P6R7","G3Q7S8","H4R8T9","I5S9U1","J6T1V2","K7U2W3","L8V3X4","M9W4Y5","N1X5Z6","O2Y6A7","P3Z7B8","Q4A8C9","R5B9D1","S6C1E2","T7D2F3","U8E3G4","V9F4H5","W1G5I6","X2H6J7","Y3I7K8","Z4J8L9","A5K9M1","B6L1N2","C7M2O3","D8N3P4","E9O4Q5","F1P5R6","G2Q6S7","H3R7T8","I4S8U9","J5T9V1","K6U1W2","L7V2X3","M8W3Y4","N9X4Z5","O1Y5A6","P2Z6B7","Q3A7C8","R4B8D9","S5C9E1","T6D1F2","U7E2G3","V8F3H4","W9G4I5","X1H5J6","Y2I6K7","Z3J7L8","A4K8M9","B5L9N1","C6M1O2","D7N2P3","E8O3Q4","F9P4R5","G1Q5S6","H2R6T7","I3S7U8","J4T8V9","K5U9W1","L6V1X2","M7W2Y3","N8X3Z4","O9Y4A5","P1Z5B6","Q2A6C7","R3B7D8","S4C8E9","T5D9F1","U6E1G2","V7F2H3","W8G3I4","X9H4J5","Y1I5K6","Z2J6L7","A3K7M8","B4L8N9","C5M9O1","D6N1P2","E7O2Q3","F8P3R4","G9Q4S5","H1R5T6","I2S6U7","J3T7V8","K4U8W9","L5V9X1","M6W1Y2","N7X2Z3","O8Y3A4","P9Z4B5","Q1A5C6","R2B6D7","S3C7E8","T4D8F9","U5E9G1","V6F1H2","W7G2I3","X8H3J4","Y9I4K5","Z1J5L6","A2K6M7","B3L7N8","C4M8O9","D5N9P1","E6O1Q2","F7P2R3","G8Q3S4","H9R4T5","I1S5U6","J2T6V7","K3U7W8","L4V8X9","M5W9Y1","N6X1Z2","O7Y2A3","P8Z3B4","Q9A4C5","R1B5D6","S2C6E7","T3D7F8","U4E8G9","V5F9H1","W6G1I2","X7H2J3","Y8I3K4","Z9J4L5","A1K5M6","B2L6N7","C3M7O8","D4N8P9","E5O9Q1","F6P1R2","G7Q2S3","H8R3T4","I9S4U5","J1T5V6","K2U6W7","L3V7X8","M4W8Y9","N5X9Z1","O6Y1A2","P7Z2B3","Q8A3C4","R9B4D5","S1C5E6","T2D6F7","U3E7G8","V4F8H9","W5G9I1","X6H1J2","Y7I2K3","Z8J3L4","A9K4M5","B1L5N6","C2M6O7","D3N7P8","E4O8Q9","F5P9R1","G6Q1S2","H7R2T3","I8S3U4","J9T4V5","K1U5W6","L2V6X7","M3W7Y8","N4X8Z9","O5Y9A1","P6Z1B2","Q7A2C3","R8B3D4","S9C4E5","T1D5F6","U2E6G7","V3F7H8","W4G8I9","X5H9J1","Y6I1K2","Z7J2L3","A8K3M4","B9L4N5","C1M5O6","D2N6P7","E3O7Q8","F4P8R9","G5Q9S1","H6R1T2","I7S2U3","J8T3V4","K9U4W5","L1V5X6","M2W6Y7","N3X7Z8","O4Y8A9","P5Z9B1","Q6A1C2","R7B2D3","S8C3E4","T9D4F5","U1E5G6","V2F6H7","W3G7I8","X4H8J9","Y5I9K1","Z6J1L2","A7K2M3","B8L3N4","C9M4O5","D1N5P6","E2O6Q7","F3P7R8","G4Q8S9","H5R9T1","I6S1U2","J7T2V3","K8U3W4","L9V4X5","M1W5Y6","N2X6Z7","O3Y7A8","P4Z8B9","Q5A9C1","R6B1D2","S7C2E3","T8D3F4","U9E4G5","V1F5H6","W2G6I7","X3H7J8","Y4I8K9","Z5J9L1","A6K1M2","B7L2N3","C8M3O4","D9N4P5","E1O5Q6","F2P6R7","G3Q7S8","H4R8T9","I5S9U1","J6T1V2","K7U2W3","L8V3X4","M9W4Y5","N1X5Z6","O2Y6A7","P3Z7B8"],t}();o.default=i,cc._RF.pop()},{}],InvitationUI:[function(t,e,o){"use strict";cc._RF.push(e,"662b2EyfNRMcIspoPI0wEDl","InvitationUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../utils/UIbase"),u=t("../utils/manager/PrefabUtil"),p=t("../manager/AudioMgr"),f=t("../datas/AudioPath"),h=t("../manager/LocalData"),d=t("../datas/InvitationCodes"),g=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.input=null,e.checkmark=null,e.btn_close=null,e.messageLabel=null,e.panel=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(console.log("=== InvitationUI.inst \u88ab\u8c03\u7528 ==="),null==this._inst||null==this._inst.node){console.log("\u9700\u8981\u521b\u5efa\u65b0\u7684InvitationUI\u5b9e\u4f8b");var t=u.default.get("InvitationUI");if(console.log("\u83b7\u53d6\u5230\u7684\u9884\u5236\u4f53:",t),!t)return console.error("\u274c InvitationUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;console.log("\u5f00\u59cb\u5b9e\u4f8b\u5316\u9884\u5236\u4f53");var e=cc.instantiate(t);if(console.log("\u9884\u5236\u4f53\u5b9e\u4f8b\u5316\u5b8c\u6210\uff0c\u8282\u70b9:",e),console.log("\u83b7\u53d6InvitationUI\u7ec4\u4ef6"),this._inst=e.getComponent(o),console.log("\u7ec4\u4ef6\u83b7\u53d6\u7ed3\u679c:",this._inst),!this._inst)return console.error("\u274c \u65e0\u6cd5\u83b7\u53d6InvitationUI\u7ec4\u4ef6\uff0c\u8bf7\u68c0\u67e5\u9884\u5236\u4f53\u662f\u5426\u6b63\u786e\u6dfb\u52a0\u4e86\u811a\u672c\u7ec4\u4ef6"),null}return console.log("\u8fd4\u56deInvitationUI\u5b9e\u4f8b:",this._inst),this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){this.initUI()},e.prototype.initUI=function(){this.input&&(this.input.string="",this.input.placeholder="\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",this.input.maxLength=6,cc.sys.isMobile?(this.input.inputMode=0,this.input.inputFlag=0,this.input.returnType=0,this.input.node.on(cc.Node.EventType.TOUCH_START,this.onInputTouch,this)):(this.input.inputMode=1,this.input.inputFlag=0,this.input.returnType=0),this.input.node.on("editing-return",this.onInputReturn,this),this.input.node.on("editing-did-began",this.onInputDidBegan,this)),this.messageLabel&&(this.messageLabel.string="",this.messageLabel.node.active=!1),this.checkmark&&this.checkmark.node.on(cc.Node.EventType.TOUCH_END,this.onClickCheckmark,this),this.btn_close&&this.btn_close.node.on(cc.Node.EventType.TOUCH_END,this.onClickClose,this)},e.prototype.onShow=function(){this.input&&(this.input.string="",this.input.placeholder="\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",this.input.enabled=!0,this.input.node.active=!0),this.messageLabel&&(this.messageLabel.string="",this.messageLabel.node.active=!1),this.playShowAnimation()},e.prototype.onHide=function(){this.hideMessage()},e.prototype.onInputTouch=function(){cc.sys.isMobile&&this.input.focus()},e.prototype.onInputDidBegan=function(){},e.prototype.showNativeInputDialog=function(){var t=this;if("undefined"!=typeof prompt){var e=prompt("\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801:");if(e){var o=e.trim();if(6!==o.length)return alert("\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801"),void this.scheduleOnce(function(){t.showNativeInputDialog()},.1);this.input&&(this.input.string=o),this.validateInvitationCodeDirect(o)}}else"undefined"!=typeof wx&&wx.showModal?wx.showModal({title:"\u8f93\u5165\u9080\u8bf7\u7801",content:"\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",editable:!0,placeholderText:"6\u4f4d\u9080\u8bf7\u7801",success:function(e){if(e.confirm&&e.content){var o=e.content.trim();if(6!==o.length)return void wx.showToast({title:"\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",icon:"none"});t.input&&(t.input.string=o),t.validateInvitationCodeDirect(o)}}}):this.showMessage("\u8bf7\u76f4\u63a5\u5728\u8f93\u5165\u6846\u4e2d\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",!1)},e.prototype.onInputReturn=function(){this.validateInvitationCode()},e.prototype.onClickCheckmark=function(){p.default.playSound(f.default.CLICK),this.input&&this.input.string&&""!==this.input.string.trim()?this.validateInvitationCode():cc.sys.isMobile?this.showNativeInputDialog():this.showMessage("\u8bf7\u5728\u8f93\u5165\u6846\u4e2d\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",!1)},e.prototype.onClickClose=function(){p.default.playSound(f.default.CLICK),this.hideUI()},e.prototype.validateInvitationCode=function(){if(this.input){var t=this.input.string.trim();this.validateInvitationCodeDirect(t)}else console.error("\u8f93\u5165\u6846\u672a\u7ed1\u5b9a")},e.prototype.validateInvitationCodeDirect=function(t){6===t.length?d.default.validateCode(t)?this.onInvitationSuccess(t):this.showMessage("\u9080\u8bf7\u7801\u9519\u8bef\uff0c\u8bf7\u91cd\u65b0\u8f93\u5165",!1):this.showMessage("\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801",!1)},e.prototype.onInvitationSuccess=function(t){var e=this;h.default.isVipUser=!0,h.default.usedRedeemCode=t,this.showMessage("\u5151\u6362\u7801\u4f7f\u7528\u6210\u529f\uff01VIP\u6743\u9650\u5df2\u6fc0\u6d3b",!0),this.scheduleOnce(function(){e.hideUI()},2),cc.systemEvent.emit("vipStatusChanged",!0)},e.prototype.showMessage=function(t,e){this.messageLabel?(this.messageLabel.string=t,this.messageLabel.node.active=!0,this.messageLabel.node.color=e?cc.Color.GREEN:cc.Color.RED,this.playMessageAnimation()):console.error("\u6d88\u606f\u6807\u7b7e\u672a\u7ed1\u5b9a")},e.prototype.hideMessage=function(){this.messageLabel&&(this.messageLabel.node.active=!1)},e.prototype.playShowAnimation=function(){console.log("=== \u64ad\u653e\u663e\u793a\u52a8\u753b ==="),this.panel?(console.log("panel\u8282\u70b9\u5b58\u5728\uff0c\u5f00\u59cb\u52a8\u753b"),console.log("panel\u521d\u59cb\u72b6\u6001 - \u4f4d\u7f6e:",this.panel.position,"\u7f29\u653e:",this.panel.scale),this.panel.scale=0,cc.tween(this.panel).to(.3,{scale:1},{easing:"backOut"}).call(function(){console.log("\u2705 \u5f39\u7a97\u52a8\u753b\u64ad\u653e\u5b8c\u6210")}).start()):console.warn("\u26a0\ufe0f panel\u8282\u70b9\u672a\u7ed1\u5b9a\uff0c\u65e0\u6cd5\u64ad\u653e\u52a8\u753b")},e.prototype.playMessageAnimation=function(){this.messageLabel&&(this.messageLabel.node.opacity=0,cc.tween(this.messageLabel.node).to(.2,{opacity:255}).start())},e.prototype.onDestroy=function(){this.input&&this.input.node.off("editing-return",this.onInputReturn,this),this.checkmark&&this.checkmark.node.off(cc.Node.EventType.TOUCH_END,this.onClickCheckmark,this),this.btn_close&&this.btn_close.node.off(cc.Node.EventType.TOUCH_END,this.onClickClose,this)},a([c(cc.EditBox)],e.prototype,"input",void 0),a([c(cc.Button)],e.prototype,"checkmark",void 0),a([c(cc.Button)],e.prototype,"btn_close",void 0),a([c(cc.Label)],e.prototype,"messageLabel",void 0),a([c(cc.Node)],e.prototype,"panel",void 0),o=a([s],e)}(l.default);o.default=g,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/InvitationCodes":"InvitationCodes","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],Load:[function(t,e,o){"use strict";cc._RF.push(e,"21e516K299GEIylKFR2AHC5","Load");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0}),o.Load=void 0;var r=t("../utils/Palt"),s=t("../utils/SubLoad"),c=t("../utils/manager/PrefabUtil"),l=cc._decorator,u=l.ccclass,p=l.property,f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.progressBar=null,e.gameVersion=null,e.progressLabel=null,e.flags=new Array,e.p=40,e.frame_ok=!1,e}return i(e,t),e.prototype.onLoad=function(){r.default.initMoban(),this.flags[0]=0,this.scheduleOnce(this.startLoad,.2)},e.prototype.startLoad=function(){var t=this;s.default.loadSub(function(){c.default.load(function(){cc.director.preloadScene("Main",function(){t.flags[0]=1})})}),r.default.init()},e.prototype.update=function(){if(this.p+=.5,this.p>98&&(this.p=98),this.p<10&&(this.p=10),this.progressBar.node.width=500*this.p/100,this.progressLabel.string="\u52a0\u8f7d\u8d44\u6e90\u4e2d... "+Math.floor(this.p)+"%",this.p>90&&0==this.frame_ok){for(var t in this.flags)if(1!=this.flags[t])return;cc.director.loadScene("Main",function(){}),this.frame_ok=!0}},a([p(cc.Sprite)],e.prototype,"progressBar",void 0),a([p(cc.Label)],e.prototype,"gameVersion",void 0),a([p(cc.Label)],e.prototype,"progressLabel",void 0),a([u],e)}(cc.Component);o.Load=f,cc._RF.pop()},{"../utils/Palt":"Palt","../utils/SubLoad":"SubLoad","../utils/manager/PrefabUtil":"PrefabUtil"}],LocalData:[function(t,e,o){"use strict";cc._RF.push(e,"994d6pS8kBHIqr46nhWmuPj","LocalData");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=t("../utils/Palt"),a=t("../utils/util/GameUtil"),r=t("../datas/Constants"),s=cc._decorator,c=s.ccclass,l=(s.property,function(){function t(){}return t.getItemToStr=function(t,e){if(null==this.str_datas[t]){var o=cc.sys.localStorage.getItem(t);null==o||""==o?(o="",e&&(o=e),this.str_datas[t]=o):this.str_datas[t]=o}return this.str_datas[t]},t.setItemToStr=function(t,e){this.str_datas[t]=e,cc.sys.localStorage.setItem(t,e)},t.getItemToNumber=function(t,e){if(null==this.int_datas[t]){var o=cc.sys.localStorage.getItem(t);if(null==o||""==o){var n=0;e&&(n=e),this.int_datas[t]=n}else this.int_datas[t]=parseInt(o)}return this.int_datas[t]},t.setItemToNumber=function(t,e){this.int_datas[t]=e,cc.sys.localStorage.setItem(t,e+"")},Object.defineProperty(t,"lv",{get:function(){return this.getItemToNumber("curLevel",1)},set:function(t){i.default.setRank(t),this.setItemToNumber("curLevel",t)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"openid",{get:function(){return this.getItemToStr("openid")},set:function(t){this.setItemToStr("openid",t)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"userinfo",{get:function(){if(null==this._userinfo){var t=this.getItemToStr("userinfo");this._userinfo=""==t?null:JSON.parse(t)}return this._userinfo},set:function(t){this._userinfo=t,this.setItemToStr("userinfo",JSON.stringify(t))},enumerable:!1,configurable:!0}),Object.defineProperty(t,"userid",{get:function(){var t=this.getItemToStr("userid");return""==t&&(t=""+Date.now()+a.default.randomRange(0,1e7),this.setItemToStr("userid",t)),t},set:function(t){this.setItemToStr("userid",t)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"catSkinList",{get:function(){var t=localStorage.getItem("local_data_list");return t||(t="{}"),JSON.parse(t)},set:function(){},enumerable:!1,configurable:!0}),Object.defineProperty(t,"userRegional",{get:function(){return this.getItemToStr("userRegional","hongkong")},set:function(t){this.setItemToStr("userRegional",t)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"currentSkin",{get:function(){return""==this.getItemToStr("currentSkin")&&this.setItemToStr("currentSkin","rabbit"),this.getItemToStr("currentSkin")},set:function(t){this.setItemToStr("currentSkin",t)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"obtainedSkins",{get:function(){return""==this.getItemToStr("obtainedSkins")&&this.setItemToStr("obtainedSkins",JSON.stringify(["rabbit"])),JSON.parse(this.getItemToStr("obtainedSkins"))},set:function(t){var e=this.obtainedSkins.concat([t.id]);this.setItemToStr("obtainedSkins",JSON.stringify(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t,"GameBgId",{get:function(){return""==this.getItemToStr("GameBgId")&&this.setItemToStr("GameBgId","bg1"),this.getItemToStr("GameBgId")},set:function(t){this.setItemToStr("GameBgId",t)},enumerable:!1,configurable:!0}),t.getBuffItemCount=function(t){var e=this.getBuffItemKey(t);return e?this.getItemToNumber(e,0):(console.error("can not get buff item key!"),0)},t.getBuffItemKey=function(t){switch(t){case r.BuffItems.AddTime:return"AddTime";case r.BuffItems.Inverse:return"Inverse";case r.BuffItems.Hint:return"Hint";case r.BuffItems.Undo:return"Undo";case r.BuffItems.SwapAnimal:return"SwapAnimal";default:return null}},t.addBuffItemCount=function(t){var e=this.getBuffItemKey(t),o=0;if(e){switch(t){case r.BuffItems.AddTime:o=r.GameSetting.addTImeReward;break;case r.BuffItems.Inverse:o=r.GameSetting.inverseReward;break;case r.BuffItems.Hint:o=r.GameSetting.hintReward;break;case r.BuffItems.Undo:o=r.GameSetting.undoReward;break;case r.BuffItems.SwapAnimal:o=r.GameSetting.SwapAnimalReward}this.setItemToNumber(e,o)}else console.error("can not get buff item key!")},t.consumeBuffItem=function(t){var e=this.getBuffItemKey(t),o=this.getItemToNumber(e);o>0&&e?this.setItemToNumber(e,o):console.error("can not get buff item key or count less than zero!")},Object.defineProperty(t,"isVipUser",{get:function(){return 1===this.getItemToNumber("isVipUser",0)},set:function(t){this.setItemToNumber("isVipUser",t?1:0)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"usedRedeemCode",{get:function(){return this.getItemToStr("usedRedeemCode","")},set:function(t){this.setItemToStr("usedRedeemCode",t)},enumerable:!1,configurable:!0}),t.str_datas={},t.int_datas={},t.yx=!0,t.yy=!0,t._userinfo=null,n([c("LocalManager")],t)}());o.default=l,cc._RF.pop()},{"../datas/Constants":"Constants","../utils/Palt":"Palt","../utils/util/GameUtil":"GameUtil"}],Log:[function(t,e,o){"use strict";cc._RF.push(e,"0b029BvOypGrbF7vpHZ9vbH","Log"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){}return t.info=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];this.test},t.log=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];this.test},t.warn=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];this.test},t.error=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];this.test},t.version="1.0.0",t.test=!1,t}();o.default=n,cc._RF.pop()},{}],Palt:[function(t,e,o){"use strict";cc._RF.push(e,"86f32Ui5ixIxbWxJ/z/FTNB","Palt"),Object.defineProperty(o,"__esModule",{value:!0});var n=t("./util/GameUtil"),i=function(){function t(){}return t.isWX=function(){return"wx"==this.plat_name},t.isCocos=function(){return"cocos"==this.plat_name},t.getGameName=function(){return this.plat_name+"_hwdfk"},t.init=function(){var t={};if(t.nickName="\u4e34\u65f6\u73a9\u5bb6"+n.default.randomRange(1,1e3),t.avatarUrl="res/1.png",this.userinfo=t,cc.sys.platform==cc.sys.WECHAT_GAME&&(this.plat_name="wx",this.pt=wx,wx.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),wx.onShareAppMessage(function(){return{title:"\u4f60\u80fd\u6551\u6551\u5c0f\u732b\u5417?",imageUrl:"https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0"}}),null!=wx.onShareTimeline&&wx.onShareTimeline(function(){return{title:"\u4f60\u80fd\u6551\u6551\u5c0f\u732b\u5417?",imageUrl:"https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0"}}),this.initAds()),null!=this.pt.onShow&&this.pt.onShow(this.onshow),this.pt.getSystemInfoSync){var e=this.pt.getSystemInfoSync();this.screenWidth=e.screenWidth,this.screenHeight=e.screenHeight}},t.setShareCallback=function(e,o){t.share_obj=e,t.fun_obj=o},t.onshow=function(){null!=t.fun_obj&&(t.fun_obj.call(t.share_obj),t.fun_obj=null,t.share_obj=null),this.state,this.state=0},t.getLaunchOptionsSync=function(){},t.showToast=function(t,e){void 0===e&&(e=2e3),null!=this.pt.showToast&&this.pt.showToast({title:t,content:t,message:t,icon:"none",duration:e})},t.hideToast=function(){null!=this.pt.hideToast&&this.pt.hideToast({})},t.shareAppMessage=function(){t.state=1,null!=this.pt.shareAppMessage?this.pt.shareAppMessage({title:"\u4f60\u80fd\u62ef\u6551\u5c0f\u52a8\u7269\u4eec\u5417?",imageUrl:"https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0",desc:"-_-\u6765\u73a9\u73a9\u5427"}):null!=t.fun_obj&&(t.fun_obj.call(t.share_obj),t.fun_obj=null,t.share_obj=null)},t.setRank=function(t){var e={event:"score",level:t+""};console.log("\u8bbe\u7f6e\u5206\u6570:",e.level),this.postMessage(e)},t.updateRank=function(){this.postMessage({event:"update"})},t.postMessage=function(t){t.type="engine";try{this.pt.getOpenDataContext().postMessage(t)}catch(e){console.log(e)}},t.isBigSize=function(){return t.screenHeight>2*t.screenWidth},t.showRewardVideo=function(t){if(null!=this.rewardVideo2&&this.rewardVideo2.offClose(o),"undefined"==typeof wx)return console.log("Not in wechat"),void t(!0);if(this.noAds)return console.log("NotADS!!!"),void t(!0);var e=wx.createRewardedVideoAd({adUnitId:"adunit-244d020efbfc3268"});this.rewardVideo2=e,e.load().then(function(){console.log("\u6fc0\u52b1\u89c6\u9891 \u5e7f\u544a\u52a0\u8f7d\u6210\u529f"),e.show()}),e.onError(function(e){console.log("\u6fc0\u52b1\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u5931\u8d25",e),t(!1)});var o=function(n){n&&n.isEnded?(console.log("res:  ",n),t(!0),e.offClose(o)):(console.log("\u64ad\u653e\u4e2d\u9014\u9000\u51fa"),t(!1))};e.onClose(o)},t.initAds=function(){var t=this;if(wx){var e=cc.view.getFrameSize();this.bannerAd=wx.createBannerAd({adUnitId:"",style:{left:0,top:e.height-140,width:e.width}}),this.bannerAd.onResize(function(){t.bannerAd.style.top=e.height-t.bannerAd.style.realHeight}),this.bannerAd.onError(function(t){console.error(t.errMsg)}),wx.createInterstitialAd&&(this.interstitialAd=wx.createInterstitialAd({adUnitId:"adunit-57ef9c5e5a322e0d"}))}},t.showBanner=function(){this.bannerAd&&this.bannerAd.show()},t.hideBanner=function(){this.bannerAd&&this.bannerAd.hide()},t.showInterstitialAd=function(){this.interstitialAd&&this.interstitialAd.show().catch(function(t){console.error("\u63d2\u5c4f\u5e7f\u544a\u663e\u793a\u5931\u8d25",t)})},t.initMoban=function(){"undefined"!=typeof wx&&(this.moban1=wx.createCustomAd({adUnitId:"adunit-c01764ef0590bf80",style:{left:10,top:200,width:76,fixed:!0}}),this.moban2=wx.createCustomAd({adUnitId:"adunit-f060b74677e53976",style:{left:wx.getSystemInfoSync().windowWidth-76,top:200,width:76,fixed:!0}}))},t.showMoban=function(){"undefined"!=typeof wx&&(this.moban1.isShow()||(this.moban1.show(),this.showMoban1()))},t.showMoban1=function(){"undefined"!=typeof wx&&this.moban2.show()},t.hidemoban=function(){this.moban1&&this.moban1.isShow()&&(this.moban1.hide(),this.moban2.hide())},t.screenWidth=750,t.screenHeight=1334,t.userinfo=null,t.plat_name="cocos",t.pt={},t.code="",t.sessionKey="",t.state=0,t.noAds=!1,t.bannerAd=null,t.interstitialAd=null,t.moban1=null,t.moban2=null,t}();o.default=i,cc._RF.pop()},{"./util/GameUtil":"GameUtil"}],PauseUI:[function(t,e,o){"use strict";cc._RF.push(e,"f391abTnq1JxpHaQjg4D58t","PauseUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/UIbase"),s=t("../manager/LocalData"),c=t("../utils/manager/PrefabUtil"),l=t("../manager/AudioMgr"),u=t("../datas/AudioPath"),p=t("./GameUI"),f=t("./HomeUI"),h=t("../utils/Palt"),d=cc._decorator,g=d.ccclass,m=d.property,y=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.autoPlayPanel=null,e.autoPlayCheckbox=null,e.autoPlayCheckMark=null,e.autoPlayLabel=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst||null==this._inst.node){var t=c.default.get("PauseUI");if(!t)return console.error("PauseUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){this.initAutoPlayUI(),cc.systemEvent.on("vipStatusChanged",this.onVipStatusChanged,this)},e.prototype.onShow=function(){h.default.showBanner(),this.updateAutoPlayUI(),this.updateVipUI()},e.prototype.initAutoPlayUI=function(){this.autoPlayLabel&&(this.autoPlayLabel.string="\u662f\u5426\u5f00\u542f\u81ea\u52a8\u73a9"),this.autoPlayCheckbox&&this.autoPlayCheckbox.on(cc.Node.EventType.TOUCH_END,this.onClickAutoPlayCheckbox,this),this.autoPlayCheckMark&&(this.autoPlayCheckMark.active=!1)},e.prototype.updateAutoPlayUI=function(){this.autoPlayCheckMark&&p.default.inst&&(this.autoPlayCheckMark.active=p.default.inst.isAutoPlaying)},e.prototype.onHide=function(){h.default.hideBanner()},e.prototype.onClickClose=function(){l.default.playSound(u.default.CLICK),p.default.inst.is_game_pause=!1,this.hideUI()},e.prototype.onClickHome=function(){this.hideUI(),f.default.inst.showUI()},e.prototype.onClickResume=function(){p.default.inst.is_game_pause=!1,this.hideUI()},e.prototype.onClickPass=function(){var t=this;l.default.playSound(u.default.CLICK),h.default.showRewardVideo(function(e){e&&(s.default.lv++,p.default.inst.onStartGame(),t.hideUI())})},e.prototype.onClickAutoPlayCheckbox=function(){console.log("=== \u52fe\u9009\u6846\u88ab\u70b9\u51fb ==="),l.default.playSound(u.default.CLICK),p.default.inst?(console.log("\u70b9\u51fb\u524d\u81ea\u52a8\u73a9\u72b6\u6001:",p.default.inst.isAutoPlaying),p.default.inst.isAutoPlaying?p.default.inst.stopAutoPlay():p.default.inst.startAutoPlay(),console.log("\u70b9\u51fb\u540e\u81ea\u52a8\u73a9\u72b6\u6001:",p.default.inst.isAutoPlaying),this.autoPlayCheckMark?(console.log("CheckMark\u8282\u70b9\u5b58\u5728\uff0c\u5f3a\u5236\u663e\u793a"),this.autoPlayCheckMark.active=!0,console.log("\u8bbe\u7f6e\u540eCheckMark.active:",this.autoPlayCheckMark.active)):console.log("CheckMark\u8282\u70b9\u4e0d\u5b58\u5728\uff01"),this.updateAutoPlayUI()):console.log("GameUI.inst\u4e0d\u5b58\u5728")},e.prototype.onVipStatusChanged=function(t){console.log("PauseUI\u6536\u5230VIP\u72b6\u6001\u53d8\u5316:",t),this.updateVipUI()},e.prototype.updateVipUI=function(){var t=s.default.isVipUser;console.log("\u66f4\u65b0VIP UI\uff0c\u5f53\u524dVIP\u72b6\u6001:",t),this.autoPlayPanel&&(this.autoPlayPanel.active=t,console.log("\u81ea\u52a8\u73a9\u9762\u677f\u663e\u793a\u72b6\u6001:",this.autoPlayPanel.active))},e.prototype.onDestroy=function(){cc.systemEvent.off("vipStatusChanged",this.onVipStatusChanged,this)},a([m(cc.Node)],e.prototype,"autoPlayPanel",void 0),a([m(cc.Node)],e.prototype,"autoPlayCheckbox",void 0),a([m(cc.Node)],e.prototype,"autoPlayCheckMark",void 0),a([m(cc.Label)],e.prototype,"autoPlayLabel",void 0),o=a([g],e)}(r.default);o.default=y,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./GameUI":"GameUI","./HomeUI":"HomeUI"}],PrefabUtil:[function(t,e,o){"use strict";cc._RF.push(e,"79035vdfwlKFq984Bn4cR7w","PrefabUtil"),Object.defineProperty(o,"__esModule",{value:!0});var n=function(){function t(){}return t.load=function(t){var e=this;cc.resources.loadDir("./prefab/",cc.Prefab,function(o,n){if(null!=n){for(var i=0;i<n.length;i++)e.objs[n[i].name]=n[i];t()}else console.error("\u9884\u5236\u4f53\u52a0\u8f7d\u5931\u8d25:",o),t()})},t.get=function(t){return 0==this.objs.hasOwnProperty(t)?(console.error("\u6ca1\u6709\u627e\u5230\u9884\u5236\u4f53---"+t),null):this.objs[t]},t.objs={},t}();o.default=n,cc._RF.pop()},{}],SettingUI:[function(t,e,o){"use strict";cc._RF.push(e,"d2706bmYxdMlJhqZw61eAjA","SettingUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/UIbase"),s=t("../manager/LocalData"),c=t("../utils/manager/PrefabUtil"),l=t("../manager/AudioMgr"),u=t("../datas/AudioPath"),p=t("../utils/Palt"),f=t("./InvitationUI"),h=t("../datas/InvitationCodes"),d=cc._decorator,g=d.ccclass,m=d.property,y=function(e){function o(){var t=null!==e&&e.apply(this,arguments)||this;return t.musicSprite=null,t.soundSprite=null,t.openFrame=null,t.closeFrame=null,t.btn_invitation=null,t.timer=null,t.waitTime=200,t.lastTime=(new Date).getTime(),t.count=0,t}var n;return i(o,e),n=o,Object.defineProperty(o,"inst",{get:function(){if(null==this._inst||null==this._inst.node){var t=c.default.get("SettingUI");if(!t)return console.error("SettingUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(n)}return this._inst},enumerable:!1,configurable:!0}),o.prototype.start=function(){this.updateMusic(),this.updateSound()},o.prototype.oclickNoAds=function(){var t=(new Date).getTime();this.count=t-this.lastTime<this.waitTime?this.count+1:1,this.lastTime=(new Date).getTime(),console.log(this.count),console.log(this.lastTime),clearTimeout(this.timer),this.count>4&&(p.default.noAds=!0,this.hideUI())},o.prototype.onClickClose=function(){l.default.playSound(u.default.CLICK),this.hideUI()},o.prototype.onClickSound=function(){l.default.playSound(u.default.CLICK),s.default.yx=!s.default.yx,this.updateSound()},o.prototype.onClickMusic=function(){l.default.playSound(u.default.CLICK),s.default.yy=!s.default.yy,1==s.default.yy?l.default.playBgm():l.default.stopBgm(),this.updateMusic()},o.prototype.updateMusic=function(){this.musicSprite.spriteFrame=1==s.default.yy?this.openFrame:this.closeFrame},o.prototype.updateSound=function(){this.soundSprite.spriteFrame=1==s.default.yx?this.openFrame:this.closeFrame},o.prototype.onClickInvitation=function(){l.default.playSound(u.default.CLICK),console.log("=== \u9080\u8bf7\u7801\u6309\u94ae\u88ab\u70b9\u51fb ==="),this.btn_invitation&&cc.tween(this.btn_invitation.node).to(.1,{scale:1.2}).to(.1,{scale:1}).start();var t="undefined"!=typeof wx,e="undefined"!=typeof window&&void 0!==window.prompt;console.log("\u8fd0\u884c\u73af\u5883 - \u5fae\u4fe1:",t,"\u6d4f\u89c8\u5668:",e);try{var o=f.default.inst;o?o.showUI():this.fallbackInvitationMethod(e)}catch(n){this.fallbackInvitationMethod(e)}},o.prototype.fallbackInvitationMethod=function(t){console.log("\u4f7f\u7528\u5907\u7528\u9080\u8bf7\u7801\u8f93\u5165\u65b9\u6cd5"),t&&"undefined"!=typeof prompt?(console.log("\u4f7f\u7528\u6d4f\u89c8\u5668prompt\u65b9\u6848"),this.showSimpleInvitationDialog()):(console.log("\u4f7f\u7528\u76f4\u63a5VIP\u8bbe\u7f6e\u65b9\u6848"),this.directVipTest())},o.prototype.showSimpleInvitationDialog=function(){console.log("=== \u5f00\u59cb\u9080\u8bf7\u7801\u8f93\u5165\u6d41\u7a0b\uff08\u65e0\u9650\u6b21\u4f7f\u7528\u6a21\u5f0f\uff09===");var t=prompt("\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801\uff08\u53ef\u65e0\u9650\u6b21\u4f7f\u7528\uff09:");t?6===t.length?h.default.validateCode(t)?(s.default.isVipUser=!0,s.default.usedRedeemCode=t,alert("\u5151\u6362\u7801\u4f7f\u7528\u6210\u529f\uff01VIP\u6743\u9650\u5df2\u6fc0\u6d3b\uff08\u53ef\u91cd\u590d\u4f7f\u7528\uff09"),cc.systemEvent.emit("vipStatusChanged",!0),console.log("\u2705 \u5151\u6362\u7801\u65e0\u9650\u6b21\u4f7f\u7528\u6210\u529f")):alert("\u9080\u8bf7\u7801\u9519\u8bef"):alert("\u8bf7\u8f93\u51656\u4f4d\u9080\u8bf7\u7801"):console.log("\u7528\u6237\u53d6\u6d88\u8f93\u5165")},o.prototype.showWechatInvitationTest=function(){console.log("=== \u5fae\u4fe1\u73af\u5883\u9080\u8bf7\u7801\u6d4b\u8bd5\uff08\u65e0\u9650\u6b21\u4f7f\u7528\u6a21\u5f0f\uff09==="),console.log("\u4f7f\u7528\u6d4b\u8bd5\u9080\u8bf7\u7801:","A7K9M2");try{var t=h.default.validateCode("A7K9M2");console.log("\u9a8c\u8bc1\u7ed3\u679c:",t),t&&(s.default.isVipUser=!0,s.default.usedRedeemCode="A7K9M2",console.log("\u2705 VIP\u72b6\u6001\u8bbe\u7f6e\u6210\u529f\uff08\u53ef\u91cd\u590d\u4f7f\u7528\uff09"),cc.systemEvent.emit("vipStatusChanged",!0))}catch(e){console.error("\u9a8c\u8bc1\u8fc7\u7a0b\u51fa\u9519:",e)}},o.prototype.directVipTest=function(){var t=this;console.log("=== \u76f4\u63a5VIP\u6d4b\u8bd5 ==="),console.log("\u5f53\u524dVIP\u72b6\u6001:",s.default.isVipUser),s.default.isVipUser?console.log("\u60a8\u5df2\u7ecf\u662fVIP\u7528\u6237\uff01"):(console.log("\u8bbe\u7f6e\u4e3aVIP\u72b6\u6001"),s.default.isVipUser=!0,s.default.usedRedeemCode="A7K9M2",console.log("VIP\u72b6\u6001\u8bbe\u7f6e\u5b8c\u6210:",s.default.isVipUser),cc.systemEvent.emit("vipStatusChanged",!0),console.log("\u2705 \u5df2\u53d1\u9001vipStatusChanged\u4e8b\u4ef6"),this.scheduleOnce(function(){t.forceRefreshPauseUI()},.1),console.log("\ud83c\udf89 VIP\u8bbe\u7f6e\u6210\u529f\uff01\u8bf7\u8fdb\u5165\u6e38\u620f\u6682\u505c\u67e5\u770b\u81ea\u52a8\u73a9\u529f\u80fd"),console.log("\ud83d\udccb \u6d4b\u8bd5\u6b65\u9aa4\uff1a1.\u5f00\u59cb\u6e38\u620f 2.\u6682\u505c\u6e38\u620f 3.\u67e5\u770b\u662f\u5426\u6709AutoPlayPanel"))},o.prototype.forceRefreshPauseUI=function(){try{var e=t("./PauseUI").default;e&&e.inst&&e.inst.node&&(console.log("\u5f3a\u5236\u5237\u65b0PauseUI\u7684VIP\u72b6\u6001"),"function"==typeof e.inst.updateVipUI&&e.inst.updateVipUI())}catch(o){console.log("PauseUI\u5b9e\u4f8b\u4e0d\u5b58\u5728\u6216\u672a\u52a0\u8f7d:",o.message)}},o.prototype.createInvitationUI=function(){this.showSimpleInvitationDialog()},a([m(cc.Sprite)],o.prototype,"musicSprite",void 0),a([m(cc.Sprite)],o.prototype,"soundSprite",void 0),a([m(cc.SpriteFrame)],o.prototype,"openFrame",void 0),a([m(cc.SpriteFrame)],o.prototype,"closeFrame",void 0),a([m(cc.Button)],o.prototype,"btn_invitation",void 0),n=a([g],o)}(r.default);o.default=y,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/InvitationCodes":"InvitationCodes","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./InvitationUI":"InvitationUI","./PauseUI":"PauseUI"}],ShopSkinItem:[function(t,e,o){"use strict";cc._RF.push(e,"660b6nCGldCsIPtFSU07hA1","ShopSkinItem");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../manager/DataMgr"),s=t("../utils/Palt"),c=t("./ShopUI"),l=cc._decorator,u=l.ccclass,p=l.property,f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.sprite=null,e.lockNode=null,e.useNode=null,e.usingNode=null,e.id=0,e}return i(e,t),e.prototype.setData=function(){var t=this;cc.resources.load("/cat/cat_skin_"+this.id,cc.SpriteFrame,function(e,o){t.sprite.spriteFrame=o});var e=r.DataMgr.ins.isUnlockSkin(this.id);this.usingNode.active=!1,this.useNode.active=!1,this.lockNode.active=!1,e?(this.lockNode.active=!1,r.DataMgr.ins.isUseSkin(this.id)?(this.useNode.active=!1,this.usingNode.active=!0):(this.useNode.active=!0,this.usingNode.active=!1)):(this.useNode.active=!1,this.lockNode.active=!0)},e.prototype.onClickUnlock=function(){var t=this;s.default.showRewardVideo(function(e){e&&(r.DataMgr.ins.unlockSkin(t.id),c.default.inst.updateNodesData())})},e.prototype.onClickUse=function(){r.DataMgr.ins.setSkin(this.id),c.default.inst.updateNodesData()},a([p(cc.Sprite)],e.prototype,"sprite",void 0),a([p(cc.Node)],e.prototype,"lockNode",void 0),a([p(cc.Node)],e.prototype,"useNode",void 0),a([p(cc.Node)],e.prototype,"usingNode",void 0),a([p(cc.Integer)],e.prototype,"id",void 0),a([u],e)}(cc.Component);o.default=f,cc._RF.pop()},{"../manager/DataMgr":"DataMgr","../utils/Palt":"Palt","./ShopUI":"ShopUI"}],ShopUI:[function(t,e,o){"use strict";cc._RF.push(e,"247db6xn8RHeKZh4LmusC9i","ShopUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/UIbase"),s=t("../utils/manager/PrefabUtil"),c=t("./ShopSkinItem"),l=cc._decorator,u=l.ccclass,p=l.property,f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.nodes=[],e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=s.default.get("ShopUI");if(!t)return console.error("ShopUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){},e.prototype.onEnable=function(){this.updateNodesData()},e.prototype.updateNodesData=function(){this.nodes.forEach(function(t){t.getComponent(c.default).setData()})},a([p([cc.Node])],e.prototype,"nodes",void 0),o=a([u],e)}(r.default);o.default=f,cc._RF.pop()},{"../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./ShopSkinItem":"ShopSkinItem"}],SimpleColorTransition:[function(t,e,o){"use strict";cc._RF.push(e,"350bc98XJtMxZWZWv48RLu0","SimpleColorTransition");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.transitionAtlas=null,e.frameCount=7,e.frameRate=6,e.isPlaying=!1,e}return i(e,t),e.prototype.playColorTransition=function(t,e,o){var n=this;!this.isPlaying&&t&&t.isValid&&this.transitionAtlas?(this.isPlaying=!0,this.playTransitionAnimation(t,function(){n.completeTransition(t,e,o)})):this.completeTransition(t,e,o)},e.prototype.playTransitionAnimation=function(t,e){var o=this,n=t.animalNode;if(n){var i=n.getComponent(cc.Animation);if(i){i.stop();var a=this.createTransitionSpriteFrames();if(0===a.length)return cc.warn("\u6362\u8272\u8fc7\u6e21\u5e27\u521b\u5efa\u5931\u8d25"),void e();for(var r=0,s=i.getClips();r<s.length;r++){var c=s[r];if("anim_color_transition"===c.name){i.removeClip(c);break}}var l=cc.AnimationClip.createWithSpriteFrames(a,this.frameRate);l.name="anim_color_transition",l.wrapMode=cc.WrapMode.Normal,l.speed=1,i.addClip(l);var u=function(t,n){"anim_color_transition"===n.name&&(i.off("stop",u,o),e())};i.on("stop",u,this),i.play("anim_color_transition")}else e()}else e()},e.prototype.createTransitionSpriteFrames=function(){for(var t=[],e=1;e<=this.frameCount;e++){for(var o=null,n=0,i=[e.toString(),e+".PNG",e+".png","frame_"+e,""+e.toString().padStart(2,"0")];n<i.length;n++){var a=i[n];if(o=this.transitionAtlas.getSpriteFrame(a))break}o?t.push(o):cc.warn("\u6362\u8272\u8fc7\u6e21\u5e27 "+e+" \u672a\u627e\u5230")}return t},e.prototype.completeTransition=function(t,e,o){this.isPlaying=!1,e&&t.setAtlas(e),o&&o()},e.prototype.isTransitionPlaying=function(){return this.isPlaying},e.prototype.setTransitionAtlas=function(t){this.transitionAtlas=t},e.prototype.previewFrames=function(){if(this.transitionAtlas){var t=this.createTransitionSpriteFrames();cc.log("\u6362\u8272\u8fc7\u6e21\u52a8\u753b\u5171\u6709 "+t.length+" \u5e27"),t.forEach(function(t,e){t&&cc.log("\u7b2c "+(e+1)+" \u5e27: "+t.name)})}else cc.warn("\u6362\u8272\u8fc7\u6e21\u56fe\u96c6\u672a\u8bbe\u7f6e")},a([c({displayName:"\u6362\u8272\u8fc7\u6e21\u56fe\u96c6",tooltip:"\u5305\u542b\u6362\u8272\u8fc7\u6e21\u52a8\u753b\u7684\u56fe\u96c6"})],e.prototype,"transitionAtlas",void 0),a([c({displayName:"\u52a8\u753b\u5e27\u6570",tooltip:"\u6362\u8272\u52a8\u753b\u7684\u603b\u5e27\u6570"})],e.prototype,"frameCount",void 0),a([c({displayName:"\u52a8\u753b\u5e27\u7387",tooltip:"\u52a8\u753b\u64ad\u653e\u7684\u5e27\u7387(FPS)"})],e.prototype,"frameRate",void 0),a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{}],SkinGalleryUI:[function(t,e,o){"use strict";cc._RF.push(e,"2e71eKe1khCxJMjHEqLHmYw","SkinGalleryUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/manager/PrefabUtil"),s=t("../utils/UIbase"),c=t("../datas/mySkinData"),l=t("../manager/LocalData"),u=cc._decorator,p=u.ccclass,f=u.property,h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.itemContainer=null,e.itemPrefab=null,e.collectionCountLabel=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=r.default.get("SkinGalleryUI");if(!t)return console.error("SkinGalleryUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.onLoad=function(){var t=this;cc.systemEvent.on("selectSkin",function(e){t.currentSkin&&t.currentSkin.changeCheckMark(),e&&(t.currentSkin=e)})},e.prototype.start=function(){},e.prototype.initializeData=function(){this.itemContainer.removeAllChildren(),this.spawnItems(),this.updateLabel()},e.prototype.updateLabel=function(){this.collectionCountLabel.string="\u6536\u85cf\u8fdb\u5ea6  "+c.default.getCollectionProgress()+"/"+c.default.skins.length},e.prototype.showUI=function(e){t.prototype.showUI.call(this,e)},e.prototype.spawnItems=function(){for(var t=c.default.getSkins(),e=0;e<t.length;e++){var o=cc.instantiate(this.itemPrefab),n=o.getComponent("skinItemBtn");n.updateUi(t[e]),t[e].id==l.default.currentSkin&&(this.currentSkin=n),this.itemContainer.addChild(o)}},a([f(cc.Node)],e.prototype,"itemContainer",void 0),a([f(cc.Prefab)],e.prototype,"itemPrefab",void 0),a([f(cc.Label)],e.prototype,"collectionCountLabel",void 0),o=a([p],e)}(s.default);o.default=h,cc._RF.pop()},{"../datas/mySkinData":"mySkinData","../manager/LocalData":"LocalData","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],StepOverUI:[function(t,e,o){"use strict";cc._RF.push(e,"c3284X08Q9Nrr6wNBcHROta","StepOverUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/Palt"),s=t("../utils/UIbase"),c=t("../utils/manager/PrefabUtil"),l=t("./FailUI"),u=t("./GameUI"),p=cc._decorator,f=p.ccclass,h=(p.property,function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isAutoPlaying=!1,e.autoClickTimer=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=c.default.get("StepOverUI");if(!t)return console.error("StepOverUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){},e.prototype.showUI=function(e){var o=this;t.prototype.showUI.call(this,e),this.isAutoPlaying=e||!1,console.log("StepOverUI\u663e\u793a\uff0c\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),this.isAutoPlaying&&(console.log("\u81ea\u52a8\u73a9\u6a21\u5f0f\uff1a3\u79d2\u540e\u81ea\u52a8\u91cd\u8bd5\u5f53\u524d\u5173\u5361"),this.autoClickTimer=setTimeout(function(){console.log("\u81ea\u52a8\u73a9\uff1a\u6a21\u62df\u70b9\u51fb\u53d6\u6d88\u6309\u94ae\u8fdb\u5165FailUI"),o.onClickCancel()},3e3))},e.prototype.onShow=function(){r.default.showBanner()},e.prototype.onHide=function(){r.default.hideBanner(),this.autoClickTimer&&(clearTimeout(this.autoClickTimer),this.autoClickTimer=null)},e.prototype.onClickAdd=function(){var t=this;r.default.showRewardVideo(function(e){e&&(u.default.inst.addStepCount(),t.hideUI())})},e.prototype.onClickCancel=function(){this.hideUI(),l.default.inst.showUI(this.isAutoPlaying)},o=a([f],e)}(s.default));o.default=h,cc._RF.pop()},{"../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./FailUI":"FailUI","./GameUI":"GameUI"}],SubLoad:[function(t,e,o){"use strict";cc._RF.push(e,"b90ff45iPBMN6x1ZA/YGZJ9","SubLoad"),Object.defineProperty(o,"__esModule",{value:!0});var n=t("./Palt"),i=function(){function t(){}return t.init=function(){this.packages.push("resources"),this.packages.push("res")},t.loadSub=function(t){this.init(),this.callback=t,0!=n.default.isWX()?n.default.pt.loadSubpackage({name:this.packages[this.index],success:this.ok.bind(this),fail:this.err.bind(this)}):this.loadComplete()},t.ok=function(){this.index++,this.index!=this.packages.length?n.default.pt.loadSubpackage({name:this.packages[this.index],success:this.ok.bind(this),fail:this.err.bind(this)}):this.loadComplete()},t.err=function(){},t.loadComplete=function(){this.callback()},t.packages=[],t.index=0,t}();o.default=i,cc._RF.pop()},{"./Palt":"Palt"}],TimeOverUI:[function(t,e,o){"use strict";cc._RF.push(e,"e0baaQDeTZFIImqHCYusgcT","TimeOverUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../utils/Palt"),s=t("../utils/UIbase"),c=t("../utils/manager/PrefabUtil"),l=t("./FailUI"),u=t("./GameUI"),p=cc._decorator,f=p.ccclass,h=(p.property,function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isAutoPlaying=!1,e.autoClickTimer=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=c.default.get("TimeOverUI");if(!t)return console.error("TimeOverUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.start=function(){},e.prototype.showUI=function(e){var o=this;t.prototype.showUI.call(this,e),this.isAutoPlaying=e||!1,console.log("TimeOverUI\u663e\u793a\uff0c\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),this.isAutoPlaying&&(console.log("\u81ea\u52a8\u73a9\u6a21\u5f0f\uff1a3\u79d2\u540e\u81ea\u52a8\u91cd\u8bd5\u5f53\u524d\u5173\u5361"),this.autoClickTimer=setTimeout(function(){console.log("\u81ea\u52a8\u73a9\uff1a\u6a21\u62df\u70b9\u51fb\u53d6\u6d88\u6309\u94ae\u8fdb\u5165FailUI"),o.onClickCancel()},3e3))},e.prototype.onShow=function(){r.default.showBanner()},e.prototype.onHide=function(){r.default.hideBanner(),this.autoClickTimer&&(clearTimeout(this.autoClickTimer),this.autoClickTimer=null)},e.prototype.onClickAdd=function(){var t=this;r.default.showRewardVideo(function(e){e&&(u.default.inst.onClickAddTime(),u.default.inst.is_game_playing=!0,t.hideUI())})},e.prototype.onClickCancel=function(){this.hideUI(),l.default.inst.showUI(this.isAutoPlaying)},o=a([f],e)}(s.default));o.default=h,cc._RF.pop()},{"../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./FailUI":"FailUI","./GameUI":"GameUI"}],TipsUI:[function(t,e,o){"use strict";cc._RF.push(e,"6c8f2j79aBOD7tErxPjuVOd","TipsUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../utils/manager/PrefabUtil"),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.tipsLabel=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=l.default.get("TipsUI");if(!t)return console.error("TipsUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.showTips=function(t){var e=this;this.tipsLabel.string=t,this.showUI(),this.node.zIndex=9999,this.node.x=cc.winSize.width/2,this.node.y=cc.winSize.height/2-40,this.node.opacity=0,null==this.tw?this.tw=cc.tween(this.node).to(.3,{opacity:255,y:cc.winSize.height/2}).delay(1).to(.3,{opacity:0,y:cc.winSize.height/2+40}).call(function(){e.hideUI()}).start():(this.tw.stop(),this.tw.start())},a([c(cc.Label)],e.prototype,"tipsLabel",void 0),o=a([s],e)}(t("../utils/UIbase").default);o.default=u,cc._RF.pop()},{"../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],UIbase:[function(t,e,o){"use strict";cc._RF.push(e,"cdb235IcWBD9JPMyupktx+7","UIbase");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(o,"__esModule",{value:!0});var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.showUI=function(){null!=this.node.parent&&(this.node.parent=null),cc.director.getScene().addChild(this.node),this.onShow()},e.prototype.hideUI=function(){this.onHide(),this.node.removeFromParent(!1)},e.prototype.onShow=function(){},e.prototype.onHide=function(){},e}(cc.Component);o.default=a,cc._RF.pop()},{}],Utility:[function(t,e,o){"use strict";cc._RF.push(e,"ae00f2fduhE5KVSSQgdzOFL","Utility");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=cc._decorator,a=i.ccclass,r=(i.property,function(){function t(){}return t.changeBackground=function(t,e){e.spriteFrame=t},n([a],t)}());o.default=r,cc._RF.pop()},{}],VipTestHelper:[function(t,e,o){"use strict";cc._RF.push(e,"d5ce5pT0PFJC48GVfSFMgDt","VipTestHelper"),Object.defineProperty(o,"__esModule",{value:!0});var n=t("../manager/LocalData"),i=t("../datas/InvitationCodes"),a=function(){function t(){}return t.resetVipStatus=function(){n.default.isVipUser=!1,n.default.usedRedeemCode="",console.log("VIP\u72b6\u6001\u5df2\u91cd\u7f6e")},t.setVipStatus=function(t){n.default.isVipUser=t,console.log("VIP\u72b6\u6001\u8bbe\u7f6e\u4e3a:",t),cc.systemEvent.emit("vipStatusChanged",t)},t.getVipStatus=function(){var t=n.default.isVipUser;return console.log("\u5f53\u524dVIP\u72b6\u6001:",t),t},t.getTestCode=function(){var t=i.default.getRandomCode();return console.log("\u6d4b\u8bd5\u9080\u8bf7\u7801:",t),t},t.testValidateCode=function(t){var e=i.default.validateCode(t);return console.log("\u9080\u8bf7\u7801 "+t+" \u9a8c\u8bc1\u7ed3\u679c:",e),e},t.getUsedCode=function(){var t=n.default.usedRedeemCode;return console.log("\u5df2\u4f7f\u7528\u7684\u9080\u8bf7\u7801:",t),t},t.printVipInfo=function(){console.log("=== VIP\u7cfb\u7edf\u72b6\u6001\u4fe1\u606f ==="),console.log("VIP\u72b6\u6001:",n.default.isVipUser),console.log("\u5df2\u4f7f\u7528\u9080\u8bf7\u7801:",n.default.usedRedeemCode),console.log("\u9080\u8bf7\u7801\u603b\u6570:",i.default.getCodeCount()),console.log("=====================")},t.getCommonTestCodes=function(){return["A7K9M2","B8L3N4","C9M4O5","D1N5P6","E2O6Q7","F3P7R8","G4Q8S9","H5R9T1","I6S1U2","J7T2V3"]},t}();o.default=a,cc._RF.pop()},{"../datas/InvitationCodes":"InvitationCodes","../manager/LocalData":"LocalData"}],WinUI:[function(t,e,o){"use strict";cc._RF.push(e,"362d0hkO8dJzLDnPjTBInIr","WinUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=t("../datas/AudioPath"),u=t("../datas/mySkinData"),p=t("../manager/AudioMgr"),f=t("../manager/LocalData"),h=t("../utils/manager/PrefabUtil"),d=t("../utils/Palt"),g=t("../utils/UIbase"),m=t("./GameUI"),y=t("./SkinGalleryUI"),_=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.cat=null,e.isAutoPlaying=!1,e.autoClickTimer=null,e.backupTimer=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=h.default.get("WinUI");if(!t)return console.error("WinUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.showUI=function(e){var o=this;if(t.prototype.showUI.call(this,e),this.isAutoPlaying=e||!1,console.log("=== WinUI\u663e\u793a ==="),console.log("\u63a5\u6536\u5230\u7684data\u53c2\u6570:",e),console.log("\u89e3\u6790\u540e\u7684\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),console.log("data\u7684\u7c7b\u578b:",typeof e),console.log("data\u662f\u5426\u4e3atrue:",!0===e),this.isAutoPlaying?(console.log("\u2705 \u81ea\u52a8\u73a9\u6a21\u5f0f\u6fc0\u6d3b\uff1a3\u79d2\u540e\u81ea\u52a8\u70b9\u51fb\u4e0b\u4e00\u5173"),this.autoClickTimer=setTimeout(function(){console.log("\ud83c\udfae \u81ea\u52a8\u73a9\uff1a\u5f00\u59cb\u6a21\u62df\u70b9\u51fb\u4e0b\u4e00\u5173\u6309\u94ae"),o.node&&o.node.active?(console.log("\u2705 WinUI\u754c\u9762\u4ecd\u7136\u6d3b\u8dc3\uff0c\u6267\u884c\u81ea\u52a8\u70b9\u51fb"),o.onClickStartGame()):console.warn("\u274c WinUI\u754c\u9762\u5df2\u5173\u95ed\uff0c\u53d6\u6d88\u81ea\u52a8\u70b9\u51fb")},3e3),this.backupTimer=setTimeout(function(){console.log("\ud83d\udd04 \u5907\u7528\u673a\u5236\uff1a\u68c0\u67e5\u662f\u5426\u9700\u8981\u5f3a\u5236\u81ea\u52a8\u70b9\u51fb"),o.node&&o.node.active&&o.isAutoPlaying&&(console.log("\u26a0\ufe0f \u4e3b\u8981\u673a\u5236\u53ef\u80fd\u5931\u6548\uff0c\u6267\u884c\u5907\u7528\u81ea\u52a8\u70b9\u51fb"),o.onClickStartGame())},6e3)):console.log("\u274c \u975e\u81ea\u52a8\u73a9\u6a21\u5f0f\uff0c\u4e0d\u4f1a\u81ea\u52a8\u8df3\u8f6c"),0!=m.default.inst.isRewardSkinLevel){var n=u.default.findMinOrderElementNotInSkins();if(n){var i="/skins/"+n.path;cc.resources.load(i,cc.SpriteFrame,function(t,e){t?console.log(t):o.cat.spriteFrame=e}),f.default.obtainedSkins=n,y.default.inst.initializeData()}}},e.prototype.onShow=function(){d.default.showBanner()},e.prototype.onHide=function(){d.default.hideBanner(),this.autoClickTimer&&(clearTimeout(this.autoClickTimer),this.autoClickTimer=null),this.backupTimer&&(clearTimeout(this.backupTimer),this.backupTimer=null)},e.prototype.onClickStartGame=function(){console.log("=== \u70b9\u51fb\u4e0b\u4e00\u5173\u6309\u94ae ==="),console.log("\u5f53\u524d\u81ea\u52a8\u73a9\u72b6\u6001:",this.isAutoPlaying),console.log("GameUI\u5b9e\u4f8b\u5b58\u5728:",!!m.default.inst),this.hideUI(),m.default.inst.onStartGame(),this.isAutoPlaying?(console.log("\u2705 \u81ea\u52a8\u73a9\u6a21\u5f0f\uff1a\u65b0\u5173\u5361\u5f00\u59cb\u540e\u6062\u590d\u81ea\u52a8\u73a9"),setTimeout(function(){console.log("\u68c0\u67e5\u6e38\u620f\u72b6\u6001 - GameUI\u5b58\u5728:",!!m.default.inst),console.log("\u68c0\u67e5\u6e38\u620f\u72b6\u6001 - \u6e38\u620f\u6b63\u5728\u8fdb\u884c:",m.default.inst?m.default.inst.is_game_playing:"GameUI\u4e0d\u5b58\u5728"),m.default.inst&&m.default.inst.is_game_playing?(console.log("\ud83c\udfae \u5f00\u59cb\u6062\u590d\u81ea\u52a8\u73a9\u529f\u80fd"),m.default.inst.startAutoPlay(),console.log("\u2705 \u81ea\u52a8\u73a9\u5df2\u6062\u590d\uff0c\u72b6\u6001:",m.default.inst.isAutoPlaying)):console.warn("\u274c \u65e0\u6cd5\u6062\u590d\u81ea\u52a8\u73a9 - \u6e38\u620f\u72b6\u6001\u5f02\u5e38")},1e3)):console.log("\u274c \u975e\u81ea\u52a8\u73a9\u6a21\u5f0f\uff0c\u4e0d\u6062\u590d\u81ea\u52a8\u73a9"),p.default.playSound(l.default.CLICK)},e.prototype.onClickShare=function(){d.default.shareAppMessage(),p.default.playSound(l.default.CLICK)},a([c(cc.Sprite)],e.prototype,"cat",void 0),o=a([s],e)}(g.default);o.default=_,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/mySkinData":"mySkinData","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil","./GameUI":"GameUI","./SkinGalleryUI":"SkinGalleryUI"}],Zh:[function(t,e,o){"use strict";cc._RF.push(e,"548f4yW5yJLUqd9Xy6YM0z2","Zh");var n=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var i=cc._decorator,a=i.ccclass,r=(i.property,function(){function t(){}return t.zh={start:"\u5f00\u59cb"},n([a],t)}());o.default=r,cc._RF.pop()},{}],buffItemBtn:[function(t,e,o){"use strict";cc._RF.push(e,"732f6hPCDJHE47dIeacQK0I","buffItemBtn");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../datas/AudioPath"),s=t("../datas/Constants"),c=t("../manager/AudioMgr"),l=t("../manager/LocalData"),u=t("../uis/BuffItemUI"),p=t("../uis/GameUI"),f=cc._decorator,h=f.ccclass,d=f.property,g=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ad=null,e.buffItem=null,e.count=null,e.identifier=null,e}return i(e,t),e.prototype.onLoad=function(){null!=this.identifier&&this.ad&&this.buffItem&&this.count?this.init():(console.log(this.identifier,this.ad,this.buffItem,this.count),console.error("\u9762\u677f\u4e0a\u7684\u9884\u5b9a\u503c\u4e3a\u7a7a\uff0c\u8bf7\u68c0\u67e5\u9762\u677f"))},e.prototype.start=function(){},e.prototype.init=function(){l.default.getBuffItemCount(this.identifier)>9999?(this.ad&&(this.ad.active=!1),this.buffItem&&this.count&&(this.buffItem.active=!0,this.count.string=l.default.getBuffItemCount(this.identifier).toString())):(this.ad&&(this.ad.active=!0),this.buffItem&&(this.buffItem.active=!1))},e.prototype.onClickAdReward=function(){var t=l.default.getBuffItemCount(this.identifier);t>0?p.default.inst.useBuffItem(this):0==t&&(p.default.inst.is_game_pause=!0,c.default.playSound(r.default.CLICK),u.default.inst.showUI(this))},a([d(cc.Node)],e.prototype,"ad",void 0),a([d(cc.Node)],e.prototype,"buffItem",void 0),a([d(cc.Label)],e.prototype,"count",void 0),a([d({type:cc.Enum(s.BuffItems),tooltip:"\u9009\u4e00\u4e2a\u5427"})],e.prototype,"identifier",void 0),a([h],e)}(cc.Component);o.default=g,cc._RF.pop()},{"../datas/AudioPath":"AudioPath","../datas/Constants":"Constants","../manager/AudioMgr":"AudioMgr","../manager/LocalData":"LocalData","../uis/BuffItemUI":"BuffItemUI","../uis/GameUI":"GameUI"}],mySkinData:[function(t,e,o){"use strict";cc._RF.push(e,"5fce9SUVHhAD5j2ga7wlg9x","mySkinData");var n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var i in e=arguments[o])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(o,"__esModule",{value:!0});var i=t("../manager/LocalData"),a=function(){function t(){}return t.getCollectionProgress=function(){var t=this;return i.default.obtainedSkins.filter(function(e){return t.skins.some(function(t){return t.id===e})}).length},t.getSkins=function(){return this.skins.sort(function(t,e){var o=i.default.obtainedSkins.includes(t.name),n=i.default.obtainedSkins.includes(e.name);return o&&!n?-1:!o&&n?1:t.name===i.default.currentSkin?-1:e.name===i.default.currentSkin?1:t.order-e.order}).map(function(t){return n(n({},t),{isCurrent:t.id===i.default.currentSkin})})},t.findMinOrderElementNotInSkins=function(){var t=this.skins.filter(function(t){return!i.default.obtainedSkins.includes(t.id)});if(0===t.length)return null;for(var e=t[0],o=0,n=t;o<n.length;o++){var a=n[o];a.order<e.order&&(e=a)}return e},t.skins=[{id:"rabbit",name:"\u5154\u5b50",path:"rabbit",order:1,isCurrent:!1},{id:"panda",name:"\u718a\u732b",path:"panda",order:2,isCurrent:!1},{id:"raccoon",name:"\u6d63\u718a",path:"raccoon",order:3,isCurrent:!1},{id:"pig",name:"\u732a",path:"pig",order:4,isCurrent:!1},{id:"cat",name:"\u732b",path:"cat",order:5,isCurrent:!1},{id:"alpaca",name:"\u7f8a\u9a7c",path:"alpaca",order:6,isCurrent:!1},{id:"tiger",name:"\u8001\u864e",path:"tiger",order:7,isCurrent:!1},{id:"kangaroo",name:"\u888b\u9f20",path:"kangaroo",order:8,isCurrent:!1},{id:"horse",name:"\u9a6c",path:"horse",order:9,isCurrent:!1},{id:"parrot",name:"\u9e66\u9e49",path:"parrot",order:10,isCurrent:!1}],t}();o.default=a,cc._RF.pop()},{"../manager/LocalData":"LocalData"}],provinceBtn:[function(t,e,o){"use strict";cc._RF.push(e,"ece01LsrVtC5a8V8DBunNMj","provinceBtn");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../../manager/LocalData"),s=cc._decorator,c=s.ccclass,l=s.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.provinceName=null,e._provinceID="",e.province=null,e.selectedButtonFrame=null,e.normalButtonFrame=null,e}return i(e,t),e.prototype.updateUi=function(t,e){this.provinceName.string=t,this._provinceID=e},e.prototype.getID=function(){return this._provinceID},e.prototype.isClicked=function(){r.default.userRegional=this._provinceID,cc.systemEvent.emit("provinceSelect",this)},e.prototype.updateBg=function(){if(this.normalButtonFrame&&this.selectedButtonFrame){var t=this.province.getComponent(cc.Sprite);t&&t.spriteFrame&&(t.spriteFrame==this.normalButtonFrame?t.spriteFrame=this.selectedButtonFrame:t.spriteFrame=this.normalButtonFrame)}},a([l(cc.Label)],e.prototype,"provinceName",void 0),a([l],e.prototype,"_provinceID",void 0),a([l(cc.Sprite)],e.prototype,"province",void 0),a([l(cc.SpriteFrame)],e.prototype,"selectedButtonFrame",void 0),a([l(cc.SpriteFrame)],e.prototype,"normalButtonFrame",void 0),a([c],e)}(cc.Component);o.default=u,cc._RF.pop()},{"../../manager/LocalData":"LocalData"}],provinceRankRow:[function(t,e,o){"use strict";cc._RF.push(e,"b33ecdCtO9Pua+xGvMSklb4","provinceRankRow");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=cc._decorator,s=r.ccclass,c=r.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.provincePosition=null,e.provinceName=null,e.provinceScore=null,e}return i(e,t),e.prototype.updateUi=function(t,e,o){this.provinceName.string=t,this.provinceScore.string=o.toString(),this.provincePosition.string=e},a([c(cc.Label)],e.prototype,"provincePosition",void 0),a([c(cc.Label)],e.prototype,"provinceName",void 0),a([c(cc.Label)],e.prototype,"provinceScore",void 0),a([s],e)}(cc.Component);o.default=l,cc._RF.pop()},{}],provincesData:[function(t,e,o){"use strict";cc._RF.push(e,"2f5ba/Z/o9ORZg/S6VCc+MZ","provincesData"),Object.defineProperty(o,"__esModule",{value:!0});var n=t("../manager/LocalData"),i=function(){function t(){}return t.getProvinceRank=function(){var t=this,e=Object.keys(this.provinces).map(function(e){return{id:e,name:t.provinces[e].name,score:t.provinces[e].score}});return e.sort(function(t,e){return e.score-t.score}),e},t.findUserProvincePercentage=function(){var t=this.getProvinceRank(),e=n.default.userRegional;if(""==e)return 1;var o=t.findIndex(function(t){return t.id===e});if(-1===o)return console.log("The user's province is not in the data"),1;var i=t.length;return(i-o)/i},t.generateRegionTeamScores=function(){var t=Date.now();if(this.hasPassedMondayMidnight(t))this.initProvinceData();else if(""!=n.default.getItemToStr("provinceData")){try{this.provinces=JSON.parse(n.default.getItemToStr(this.localDataName))}catch(e){console.log(e)}this.increaseRegionTeamScoresByPercentage()}else this.initProvinceData();n.default.setItemToNumber("lastRunTime",t)},t.increaseRegionTeamScoresByPercentage=function(){for(var t in this.provinces)this.provinces.hasOwnProperty(t)&&(this.provinces[t].score=this.incrementValue(this.provinces[t].score));n.default.setItemToStr(this.localDataName,JSON.stringify(this.provinces))},t.initProvinceData=function(){for(var t in this.provinces)this.provinces.hasOwnProperty(t)&&(this.provinces[t].score=Math.floor(501*Math.random())+500);n.default.setItemToStr(this.localDataName,JSON.stringify(this.provinces))},t.incrementValue=function(t){var e=2*Math.random()+1;return Math.floor(t*(1+e/100))},t.hasPassedMondayMidnight=function(t){var e=new Date(t);if(0==n.default.getItemToNumber("lastRunTime"))return n.default.setItemToNumber("lastRunTime",t),!0;var o=new Date(n.default.getItemToNumber("lastRunTime")),i=e.getTime()-o.getTime();if(Math.floor(i/864e5)>=7)return!0;var a=new Date(o);a.setDate(o.getDate()-o.getDay()+1),a.setHours(0,0,0,0);var r=new Date(e);return r.setDate(e.getDate()-e.getDay()+1),r.setHours(0,0,0,0),r.getTime()>a.getTime()},t.provinces={anhui:{name:"\u5b89\u5fbd",score:2143},beijing:{name:"\u5317\u4eac",score:2089},chongqing:{name:"\u91cd\u5e86",score:1935},fujian:{name:"\u798f\u5efa",score:2105},gansu:{name:"\u7518\u8083",score:2122},guangdong:{name:"\u5e7f\u4e1c",score:1912},guangxi:{name:"\u5e7f\u897f",score:2070},guizhou:{name:"\u8d35\u5dde",score:1932},hainan:{name:"\u6d77\u5357",score:2087},hebei:{name:"\u6cb3\u5317",score:2069},heilongjiang:{name:"\u9ed1\u9f99\u6c5f",score:2145},henan:{name:"\u6cb3\u5357",score:2056},hubei:{name:"\u6e56\u5317",score:2023},hunan:{name:"\u6e56\u5357",score:2135},jiangsu:{name:"\u6c5f\u82cf",score:2186},jiangxi:{name:"\u6c5f\u897f",score:2082},jilin:{name:"\u5409\u6797",score:1904},liaoning:{name:"\u8fbd\u5b81",score:2085},neimenggu:{name:"\u5185\u8499\u53e4",score:2115},ningxia:{name:"\u5b81\u590f",score:2121},qinghai:{name:"\u9752\u6d77",score:2200},shandong:{name:"\u5c71\u4e1c",score:2047},shanghai:{name:"\u4e0a\u6d77",score:1942},shanxi:{name:"\u5c71\u897f",score:2160},shanxi2:{name:"\u9655\u897f",score:2068},sichuan:{name:"\u56db\u5ddd",score:1947},tianjin:{name:"\u5929\u6d25",score:2130},xinjiang:{name:"\u65b0\u7586",score:2102},xizang:{name:"\u897f\u85cf",score:2058},yunnan:{name:"\u4e91\u5357",score:2180},zhejiang:{name:"\u6d59\u6c5f",score:2022},hongkong:{name:"\u9999\u6e2f",score:198},macau:{name:"\u6fb3\u95e8",score:210},taiwan:{name:"\u53f0\u6e7e",score:29}},t.localDataName="provinceData",t}();o.default=i,cc._RF.pop()},{"../manager/LocalData":"LocalData"}],rankUI:[function(t,e,o){"use strict";cc._RF.push(e,"5bcfexuak9EfJ8KL1AMkbBG","rankUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../manager/LocalData"),s=t("../utils/Palt"),c=t("../utils/UIbase"),l=t("../utils/manager/PrefabUtil"),u=cc._decorator,p=u.ccclass,f=u.property,h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.view=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=l.default.get("rankUI");if(!t)return console.error("rankUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.showUI=function(e){t.prototype.showUI.call(this,e),s.default.setRank(r.default.lv),s.default.updateRank()},a([f(cc.SubContextView)],e.prototype,"view",void 0),o=a([p],e)}(c.default);o.default=h,cc._RF.pop()},{"../manager/LocalData":"LocalData","../utils/Palt":"Palt","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],regionalUI:[function(t,e,o){"use strict";cc._RF.push(e,"f6453GVy8tI/art+cpEWP5/","regionalUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../manager/LocalData"),s=t("../utils/UIbase"),c=t("../utils/manager/PrefabUtil"),l=t("../datas/provincesData"),u=cc._decorator,p=u.ccclass,f=u.property,h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.scrollViewContent=null,e.regionalItemPrefab=null,e.regionalUserItemPrefab=null,e}var o;return i(e,t),o=e,Object.defineProperty(e,"inst",{get:function(){if(null==this._inst){var t=c.default.get("regionalUI");if(!t)return console.error("regionalUI\u9884\u5236\u4f53\u672a\u627e\u5230"),null;var e=cc.instantiate(t);this._inst=e.getComponent(o)}return this._inst},enumerable:!1,configurable:!0}),e.prototype.showUI=function(e){t.prototype.showUI.call(this,e),this.spawnItems()},e.prototype.spawnItems=function(){for(var t=l.default.getProvinceRank(),e=0;e<t.length;e++){var o=cc.instantiate(this.regionalItemPrefab);r.default.userRegional==t[e].id&&(o=cc.instantiate(this.regionalUserItemPrefab)),o.getComponent("provinceRankRow").updateUi(t[e].name,""+(e+1),t[e].score),this.scrollViewContent.addChild(o)}},a([f(cc.Node)],e.prototype,"scrollViewContent",void 0),a([f(cc.Prefab)],e.prototype,"regionalItemPrefab",void 0),a([f(cc.Prefab)],e.prototype,"regionalUserItemPrefab",void 0),o=a([p],e)}(s.default);o.default=h,cc._RF.pop()},{"../datas/provincesData":"provincesData","../manager/LocalData":"LocalData","../utils/UIbase":"UIbase","../utils/manager/PrefabUtil":"PrefabUtil"}],skinItemBtn:[function(t,e,o){"use strict";cc._RF.push(e,"8146bW1aYJGC5CS3po6rmoC","skinItemBtn");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../../manager/LocalData"),s=t("../../uis/TipsUI"),c=cc._decorator,l=c.ccclass,u=c.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.grayBG=null,e.charactor=null,e.checkMark=null,e.id=null,e}return i(e,t),e.prototype.start=function(){},e.prototype.updateUi=function(t){t.id&&(this.id=t.id,this.chageSpriteFrame(t.path),r.default.obtainedSkins.includes(t.id)?(this.grayBG.node.active=!1,this.charactor.node.color=cc.color(255,255,255,255),t.isCurrent?(this.checkMark.node.active=!0,this.sendEvent()):this.checkMark.node.active=!1):(this.grayBG.node.active=!0,this.charactor.node.color=cc.color(8,7,7,255)))},e.prototype.chageSpriteFrame=function(t){var e=this,o="/skins/"+t;cc.resources.load(o,cc.SpriteFrame,function(t,o){t?console.log(t):e.charactor.spriteFrame=o})},e.prototype.changeCheckMark=function(){this.checkMark.node.active=!this.checkMark.node.active},e.prototype.onClick=function(){0==this.grayBG.node.active?0==this.checkMark.node.active?(r.default.currentSkin=this.id,this.checkMark.node.active=!0,this.sendEvent(),s.default.inst.showTips("\u76ae\u80a4\u5df2\u66f4\u6362")):s.default.inst.showTips("\u5df2\u662f\u5f53\u524d\u76ae\u80a4"):s.default.inst.showTips("\u8bf7\u8d62\u5f97\u66f4\u591a\u5173\u5361\u83b7\u5f97\u65b0\u7684\u76ae\u80a4\uff01")},e.prototype.sendEvent=function(){cc.systemEvent.emit("selectSkin",this)},a([u(cc.Sprite)],e.prototype,"grayBG",void 0),a([u(cc.Sprite)],e.prototype,"charactor",void 0),a([u(cc.Sprite)],e.prototype,"checkMark",void 0),a([l],e)}(cc.Component);o.default=p,cc._RF.pop()},{"../../manager/LocalData":"LocalData","../../uis/TipsUI":"TipsUI"}],taskProgressUI:[function(t,e,o){"use strict";cc._RF.push(e,"1f3c4JlmDRGaZwO9FwP0y5p","taskProgressUI");var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),a=this&&this.__decorate||function(t,e,o,n){var i,a=arguments.length,r=a<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,o,n);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(r=(a<3?i(r):a>3?i(e,o,r):i(e,o))||r);return a>3&&r&&Object.defineProperty(e,o,r),r};Object.defineProperty(o,"__esModule",{value:!0});var r=t("../datas/mySkinData"),s=cc._decorator,c=s.ccclass,l=s.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.SpriteNode=null,e.SpriteNodeInShadow=null,e}return i(e,t),e.prototype.onEnable=function(){var t=r.default.findMinOrderElementNotInSkins();t&&this.updateSprite(t)},e.prototype.updateSprite=function(t){var e=this,o="/skins/"+t.path;cc.resources.load(o,cc.SpriteFrame,function(t,o){t?console.log(t):(e.SpriteNode.spriteFrame=o,e.SpriteNodeInShadow.spriteFrame=o)})},a([l(cc.Sprite)],e.prototype,"SpriteNode",void 0),a([l(cc.Sprite)],e.prototype,"SpriteNodeInShadow",void 0),a([c],e)}(cc.Component);o.default=u,cc._RF.pop()},{"../datas/mySkinData":"mySkinData"}]},{},["GameMain","AudioPath","CatInfo","Constants","DirectionType","InvitationCodes","mySkinData","provincesData","AnimalColorTransition","AnimalItem","AnimalItemFrames","CatItem","Grass","SimpleColorTransition","AudioMgr","DataMgr","LocalData","buffItemBtn","AnimalColorBtn","AnimalYard","provinceBtn","provinceRankRow","skinItemBtn","AnimalColorBtnTest","BuffItemUI","ChangeGameBgUI","ChooseAreaUI","ComboUI","FailUI","GameUI","HomeUI","InvitationUI","Load","PauseUI","SettingUI","ShopSkinItem","ShopUI","SkinGalleryUI","StepOverUI","TimeOverUI","TipsUI","WinUI","rankUI","regionalUI","taskProgressUI","Palt","SubLoad","UIbase","Utility","VipTestHelper","PrefabUtil","GameUtil","Log","I18n","I18nLable","En","Zh"]);